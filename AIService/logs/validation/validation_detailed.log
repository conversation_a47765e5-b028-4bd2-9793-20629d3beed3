2025-08-03 13:26:35 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 13:26:35 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 13:26:35 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 13:26:35 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 13:26:35 | INFO     | VALIDATION | Generating outline...
2025-08-03 13:26:35 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:26:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:27:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:27:05 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:27:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:27:51 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:27:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:28:17 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:28:17 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:28:17 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:29:09 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:29:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:29:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:29:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:29:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:30:37 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 13:30:37 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 13:30:37 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 13:30:37 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 13:30:38 | INFO     | VALIDATION | Generating outline...
2025-08-03 13:30:38 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:30:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:31:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:31:04 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:31:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:32:05 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:32:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:32:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:32:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:32:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:33:39 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:33:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:33:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:33:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:33:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:34:54 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:34:54 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:34:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:34:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:34:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:35:43 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:35:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:35:44 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:35:44 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:35:44 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:36:55 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:36:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:36:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:36:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:36:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:37:38 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:37:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:37:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:37:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:37:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:38:38 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:38:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:38:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:38:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:38:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:39:36 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:39:36 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:39:38 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:39:38 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:39:38 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:40:43 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:40:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:40:45 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:40:45 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:40:45 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:41:44 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:41:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:41:46 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:41:46 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:41:46 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:42:39 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:42:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:42:41 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:42:41 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:42:41 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:44:04 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:44:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:44:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:44:05 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:44:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:45:07 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:45:07 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:45:09 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:45:09 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:45:09 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:46:13 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:46:13 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:46:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:46:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:46:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:47:22 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:47:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:47:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:47:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:47:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:48:23 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:48:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:48:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:48:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:48:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:49:28 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:49:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:49:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:49:30 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:49:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:50:27 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:50:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:50:29 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:50:29 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:50:29 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:51:34 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:51:34 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:51:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:51:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:51:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:52:37 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:52:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:52:39 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:52:39 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:52:39 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:53:39 | INFO     | VALIDATION | Outline generated successfully and saved to generated_outline_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2.json
2025-08-03 13:53:39 | INFO     | VALIDATION | Generated outline contains 5 main sections
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 1: Tab A - Proposal Cover/Transmittal Letter (0 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 2: Tab B - Factor 1 - Staffing & Key Personnel Qualifications (4 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 3: Tab C - Factor 2 - Management Approach (3 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 4: Tab D - Factor 3 - Technical Approach (6 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 5: Tab E - Factor 4 - Demonstrated Corporate Experience (3 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION | Outline generation completed successfully!
2025-08-03 14:49:34 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 14:49:34 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 14:49:34 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 14:49:34 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:49:34 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:11 | WARNING  | VALIDATION | Timeout getting requirements context for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 14:50:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:50:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:50:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:50:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:51:53 | ERROR    | VALIDATION | Failed to parse outline for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 14:51:53 | ERROR    | VALIDATION | Error generating enhanced outline for Tab A - Proposal Cover/Transmittal Letter: Failed to generate valid outline for section: Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:08:27 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:08:27 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 15:08:27 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 15:08:27 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 15:08:28 | INFO     | VALIDATION | Generating outline...
2025-08-03 15:08:28 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 15:08:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:08:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:08:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:08:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:08:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:09:42 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 15:09:42 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:09:42 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:09:55 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:09:55 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:09:55 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:11:01 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 15:11:01 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:11:01 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:11:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:11:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:11:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:11:38 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:11:39 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 15:11:39 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:11:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:11:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:08 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:08 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:08 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:09 | WARNING  | VALIDATION | Timeout getting requirements context for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:12:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:12:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:09 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:09 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:09 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:12:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:12 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:12 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:12 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:15 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:12:15 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:12 | INFO     | VALIDATION | Generating enhanced outline for: 2.0 - Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-03 15:13:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:27 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:31 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:31 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:31 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:50 | INFO     | VALIDATION | Generating enhanced outline for: 2.1 - Recruitment, Hiring, and Retention Approach
2025-08-03 15:14:50 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:14:50 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:51 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:14:51 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:14:51 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:14:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:14:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:14:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:15:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:15:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:15:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:03 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:15:03 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:15:04 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:15:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:08 | INFO     | VALIDATION | Generating enhanced outline for: 2.2 - Certifications and Training Processes
2025-08-03 15:16:08 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:08 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:10 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:10 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:10 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:19 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:19 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:47 | INFO     | VALIDATION | Generating enhanced outline for: 2.3 - Resume of Proposed Key Personnel
2025-08-03 15:17:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:17:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:48 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:17:48 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:17:48 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:53 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:17:53 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:53 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:17:53 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:17:53 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:17:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:17:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:17:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:18:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:18:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:18:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:18:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:18:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:18:58 | INFO     | VALIDATION | Generating enhanced outline for: 2.4 - Tentative/Contingent Offer Letter
2025-08-03 15:18:58 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:18:58 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:01 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:01 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:01 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:19:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:19:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:19:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:22 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:22 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:22 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:17 | INFO     | VALIDATION | Generating enhanced outline for: 3.0 - Tab C - Factor 2 - Management Approach
2025-08-03 15:20:17 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:17 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:19 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:19 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:19 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:27 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:32 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:32 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:32 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:15 | INFO     | VALIDATION | Generating enhanced outline for: 3.1 - Employee Turnover and Solutions
2025-08-03 15:21:15 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:15 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:18 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:18 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:18 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:31 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:31 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:31 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:33:20 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:33:20 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 15:33:20 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:33:20 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:33:20 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:33:35 | WARNING  | VALIDATION | Timeout getting context for Tab A - Proposal Cover/Transmittal Letter - using fallback
2025-08-03 15:33:48 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:35:45 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:35:45 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 15:35:45 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:35:45 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:35:45 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:35:58 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:35:58 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:35:58 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:36:05 | ERROR    | VALIDATION | Timeout getting context for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:36:22 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:36:22 | INFO     | VALIDATION | Section title: Test Section, Section Description: Test
2025-08-03 15:36:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:36:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:36:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:36:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:36:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 16:06:56 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 16:06:56 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 16:06:56 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 16:06:56 | INFO     | VALIDATION | Generating outline...
2025-08-03 16:06:56 | INFO     | VALIDATION | Processing 5 sections in parallel for outline generation
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:07:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:07:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:07:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:07:28 | WARNING  | VALIDATION | ChromaDB timeout for section Tab B - Factor 1 - Staffing & Key Personnel Qualifications - proceeding without context
2025-08-03 16:09:13 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 16:09:13 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:09:13 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:09:13 | WARNING  | VALIDATION | ChromaDB timeout for section Tab A - Proposal Cover/Transmittal Letter - proceeding without context
2025-08-03 16:10:29 | WARNING  | VALIDATION | ChromaDB timeout for section Tab C - Factor 2 - Management Approach - proceeding without context
2025-08-03 16:11:59 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 16:11:59 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:11:59 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:11:59 | WARNING  | VALIDATION | ChromaDB timeout for section Tab D - Factor 3 - Technical Approach - proceeding without context
2025-08-03 16:13:20 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 16:13:20 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:13:20 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:13:20 | WARNING  | VALIDATION | ChromaDB timeout for section Tab E - Factor 4 - Demonstrated Corporate Experience - proceeding without context
2025-08-03 16:14:40 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:14:40 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:14:40 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:14:40 | WARNING  | VALIDATION | ChromaDB timeout for section Recruitment, Hiring, and Retention Approach - proceeding without context
2025-08-03 16:15:45 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 16:15:45 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:15:45 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:15:45 | WARNING  | VALIDATION | ChromaDB timeout for section Employee Turnover and Solutions - proceeding without context
2025-08-03 16:17:18 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 16:17:18 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:17:18 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:17:18 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 1 – Program Management and Administration - proceeding without context
2025-08-03 16:18:44 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 16:18:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:18:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:18:44 | WARNING  | VALIDATION | ChromaDB timeout for section Experience Example 1 - proceeding without context
2025-08-03 16:20:10 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:20:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:20:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:20:10 | WARNING  | VALIDATION | ChromaDB timeout for section Certifications and Training Processes - proceeding without context
2025-08-03 16:21:36 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 16:21:36 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:21:36 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:21:36 | WARNING  | VALIDATION | ChromaDB timeout for section Surge Support Availability - proceeding without context
2025-08-03 16:23:05 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 16:23:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:23:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:23:05 | WARNING  | VALIDATION | ChromaDB timeout for section Experience Example 2 - proceeding without context
2025-08-03 16:24:28 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:24:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:24:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:24:28 | WARNING  | VALIDATION | ChromaDB timeout for section Resume of Proposed Key Personnel - proceeding without context
2025-08-03 16:25:41 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 16:25:41 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:25:41 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:25:41 | WARNING  | VALIDATION | ChromaDB timeout for section Quality Control and Performance Monitoring - proceeding without context
2025-08-03 16:26:59 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 2 – Information Management - proceeding without context
2025-08-03 16:28:21 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 16:28:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:28:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:28:21 | WARNING  | VALIDATION | ChromaDB timeout for section Experience Example 3 - proceeding without context
2025-08-03 16:29:06 | WARNING  | VALIDATION | ChromaDB timeout for section Tentative/Contingent Offer Letter - proceeding without context
2025-08-03 16:29:49 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 3 – Program Compliance - proceeding without context
2025-08-03 16:30:33 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 16:30:33 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:30:33 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:30:49 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:30:49 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:30:49 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:31:10 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 4 – Training and Outreach - proceeding without context
2025-08-03 16:32:09 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 16:32:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:32:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:32:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:32:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:32:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:32:29 | INFO     | VALIDATION | Retrieved 3 context chunks for section: TASK 5 – Regulatory Support
2025-08-03 16:33:12 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 16:33:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:33:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:33:14 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:33:14 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:33:14 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:33:22 | INFO     | VALIDATION | Retrieved 3 context chunks for section: TASK 6 – Optional – Surge
2025-08-03 16:34:37 | INFO     | VALIDATION | Successfully generated 5 outlines
2025-08-03 16:34:37 | INFO     | VALIDATION | Outline generated successfully and saved to generated_outline_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2.json
2025-08-03 16:34:37 | INFO     | VALIDATION | Generated outline contains 5 main sections
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 1: Tab A - Proposal Cover/Transmittal Letter (0 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 2: Tab B - Factor 1 - Staffing & Key Personnel Qualifications (4 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 3: Tab C - Factor 2 - Management Approach (3 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 4: Tab D - Factor 3 - Technical Approach (6 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 5: Tab E - Factor 4 - Demonstrated Corporate Experience (3 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION | Outline generation completed successfully!
2025-08-03 16:39:09 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 16:39:29 | INFO     | VALIDATION | get_opportunity called with opportunity_id=iRiYNgd8RC, tenant_id=8d9e9729-f7bd-44a0-9cf1-777f532a2db2, source=custom
2025-08-03 16:39:29 | INFO     | VALIDATION | Searching CUSTOM for opportunity_id=iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | CUSTOM search result: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-03 16:39:39 | INFO     | VALIDATION | Returning opportunity record: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-03 16:39:39 | INFO     | VALIDATION | Processing 5 sections in parallel for draft generation
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:52 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:39:52 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:39:52 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:39:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:39:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:56 | INFO     | VALIDATION | Retrieved RFP context for section: Tab A - Proposal Cover/Transmittal Letter
2025-08-03 16:39:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:39:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:39:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:39:56 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:39:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:39:57 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:39:57 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:39:57 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:40:02 | INFO     | VALIDATION | Retrieved client context for section: Tab A - Proposal Cover/Transmittal Letter
2025-08-03 16:40:47 | INFO     | VALIDATION | Retrieved RFP context for section: Tab C - Factor 2 - Management Approach
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Tab D - Factor 3 - Technical Approach
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:40:47 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:40:47 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:40:47 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:40:47 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | ERROR    | VALIDATION | Error getting ChromaDB instance mapping for unique_id adeptengineeringsolutions and tenant_id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2: Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)
2025-08-03 16:42:10 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:42:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:42:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:42:10 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Tab D - Factor 3 - Technical Approach
2025-08-03 16:43:09 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 16:43:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:43:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:43:09 | WARNING  | VALIDATION | ChromaDB operation error for collection 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions:  - continuing without context
2025-08-03 16:44:51 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 16:44:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:44:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:44:51 | WARNING  | VALIDATION | ChromaDB operation error for collection 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions:  - continuing without context
2025-08-03 16:45:38 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:45:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 1 – Program Management and Administration
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Employee Turnover and Solutions
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Experience Example 1
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:45:40 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:45:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:49 | INFO     | VALIDATION | Retrieved client context for section: Experience Example 1
2025-08-03 16:46:26 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:46:26 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:46:26 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:46:26 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Employee Turnover and Solutions
2025-08-03 16:47:02 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 16:47:02 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:47:02 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:47:02 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: TASK 1 – Program Management and Administration
2025-08-03 16:47:29 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:47:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:47:29 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Recruitment, Hiring, and Retention Approach
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:47:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:29 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Experience Example 2
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:47:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:47:31 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:47:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:32 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Surge Support Availability
2025-08-03 16:47:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:47:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:35 | INFO     | VALIDATION | Retrieved client context for section: Experience Example 2
2025-08-03 16:48:35 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:48:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:48:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:48:35 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Surge Support Availability
2025-08-03 16:49:41 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:49:41 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:49:41 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 2 – Information Management
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:49:41 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:49:41 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Recruitment, Hiring, and Retention Approach
2025-08-03 16:50:46 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:50:46 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:50:46 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Experience Example 3
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:50:46 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:50:46 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Quality Control and Performance Monitoring
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:50:46 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:50:46 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: TASK 2 – Information Management
2025-08-03 16:51:51 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:51:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:51:51 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Certifications and Training Processes
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:51:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:51:51 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Quality Control and Performance Monitoring
2025-08-03 16:52:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 3 – Program Compliance
2025-08-03 16:52:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:52:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:52:38 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Certifications and Training Processes
2025-08-03 16:53:30 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 16:53:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:53:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:53:30 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Experience Example 3
2025-08-03 16:54:21 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: TASK 3 – Program Compliance
2025-08-03 16:55:11 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:55:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:55:11 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Resume of Proposed Key Personnel
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:55:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:55:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:55:21 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:55:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:55:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:55:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:55:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:55:24 | INFO     | VALIDATION | Retrieved client context for section: Resume of Proposed Key Personnel
2025-08-03 16:56:04 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:56:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:56:04 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 4 – Training and Outreach
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:56:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:56:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:56:04 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:56:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:56:24 | INFO     | VALIDATION | Retrieved client context for section: TASK 4 – Training and Outreach
2025-08-03 16:57:00 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 16:57:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:57:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:57:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:57:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:57:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:57:07 | INFO     | VALIDATION | Retrieved RFP context for section: Tentative/Contingent Offer Letter
2025-08-03 16:57:07 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:57:07 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:07 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:57:07 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:57:07 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:14 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:57:14 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:57:14 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:57:18 | INFO     | VALIDATION | Retrieved client context for section: Tentative/Contingent Offer Letter
2025-08-03 16:57:44 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 5 – Regulatory Support
2025-08-03 16:57:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:57:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:44 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:57:44 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:57:44 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:46 | INFO     | VALIDATION | Retrieved client context for section: TASK 5 – Regulatory Support
2025-08-03 16:58:37 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 16:58:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:58:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:58:39 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:58:39 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:58:39 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:58:44 | INFO     | VALIDATION | Retrieved RFP context for section: TASK 6 – Optional – Surge
2025-08-03 16:58:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:58:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:58:45 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:58:45 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:58:45 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:58:48 | INFO     | VALIDATION | Retrieved client context for section: TASK 6 – Optional – Surge
2025-08-03 16:59:27 | INFO     | VALIDATION | Successfully generated 5 draft sections
2025-08-03 16:59:58 | INFO     | VALIDATION | Updated CustomOppsTable record with opportunity_id=iRiYNgd8RC
2025-08-03 17:18:35 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 17:19:22 | INFO     | VALIDATION | Database initialized successfully
2025-08-03 17:19:22 | INFO     | VALIDATION | Starting all schedulers...
2025-08-03 17:19:22 | INFO     | VALIDATION | Proposal scheduler started with 60 second interval
2025-08-03 17:19:22 | INFO     | VALIDATION | Custom opps scheduler started with 60 second interval
2025-08-03 17:19:22 | INFO     | VALIDATION | SAM opps scheduler started with 60 second interval
2025-08-03 17:19:22 | INFO     | VALIDATION | Client process queue scheduler started with 60 second interval
2025-08-03 17:19:22 | INFO     | VALIDATION | Datametastore queue scheduler started with 60 second interval
2025-08-03 17:19:22 | INFO     | VALIDATION | Simulation scheduler started with 60 second interval
2025-08-03 17:19:22 | INFO     | VALIDATION | All schedulers started
2025-08-03 17:19:22 | INFO     | VALIDATION | All schedulers started successfully
2025-08-03 17:19:22 | INFO     | VALIDATION | Schedulers are disabled on startup (via env variable)
2025-08-03 17:20:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:20:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:20:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:20:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:20:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:20:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:21:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:21:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:21:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:21:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:21:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:21:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:22:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:22:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:22:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:22:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:22:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:22:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:23:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:23:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:23:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:23:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:23:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:23:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:23:39 | INFO     | VALIDATION | Processing cover page with ID: 4195
2025-08-03 17:24:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:24:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:24:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:24:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:24:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:24:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:24:27 | INFO     | VALIDATION | Found cover page: cover-page-1751947116383.png
2025-08-03 17:24:27 | INFO     | VALIDATION | Using compliance settings for cover page: font=Times-Roman, body_size=12, header_size=14
2025-08-03 17:24:27 | INFO     | VALIDATION | Processing cover page with content type: image/png
2025-08-03 17:24:27 | INFO     | VALIDATION | Original image size: 1600 x 2000
2025-08-03 17:24:28 | INFO     | VALIDATION | Resized image to print quality: 2550 x 3300 pixels
2025-08-03 17:24:28 | INFO     | VALIDATION | Created image element to cover entire page with text overlay
2025-08-03 17:24:28 | INFO     | VALIDATION | Successfully created image cover page from: cover-page-1751947116383.png
2025-08-03 17:24:28 | INFO     | VALIDATION | Cover page with text overlay prepared: 1 elements
2025-08-03 17:24:28 | INFO     | VALIDATION | Converting draft with 5 sections
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing section 1: <class 'dict'>
2025-08-03 17:24:28 | INFO     | VALIDATION | Section title: 1.0 Tab A - Proposal Cover/Transmittal Letter
2025-08-03 17:24:28 | INFO     | VALIDATION | Section content length: 1133 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Content preview: COVER LETTER
August 03, 2025
[Government Agency]
[Agency Address]

Reference: Export Controls Group Support
Solicitation: iRiYNgd8RC

Dear Contracting Officer,

Adept Engineering Solutions is pleased ...
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing section 2: <class 'dict'>
2025-08-03 17:24:28 | INFO     | VALIDATION | Section title: 2.0 Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-03 17:24:28 | INFO     | VALIDATION | Section content length: 4543 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions will assemble a dedicated team possessing the requisite expertise to deliver exceptional results. Our approach prioritizes a collaborative, multi-disciplinary structure, en...
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing 4 subsections
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 title: 2.1 Recruitment, Hiring, and Retention Approach
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 content length: 5596 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 title: 2.2 Certifications and Training Processes
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 content length: 5498 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 title: 2.3 Resume of Proposed Key Personnel
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 content length: 5354 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 4 title: 2.4 Tentative/Contingent Offer Letter
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 4 content length: 1169 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing section 3: <class 'dict'>
2025-08-03 17:24:28 | INFO     | VALIDATION | Section title: 3.0 Tab C - Factor 2 - Management Approach
2025-08-03 17:24:28 | INFO     | VALIDATION | Section content length: 5836 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Content preview: Our approach to managing this contract centers on proactive risk management, rigorous quality assurance, and transparent communication, ensuring consistent delivery of high-quality services that meet ...
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing 3 subsections
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 title: 3.1 Employee Turnover and Solutions
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 content length: 5755 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 title: 3.2 Surge Support Availability
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 content length: 4561 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 title: 3.3 Quality Control and Performance Monitoring
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 content length: 4944 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing section 4: <class 'dict'>
2025-08-03 17:24:28 | INFO     | VALIDATION | Section title: 4.0 Tab D - Factor 3 - Technical Approach
2025-08-03 17:24:28 | INFO     | VALIDATION | Section content length: 5319 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Content preview: Our approach to achieving the project objectives centers on a phased, iterative methodology leveraging Agile principles and Systems Engineering best practices. We will deliver a robust and scalable so...
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing 6 subsections
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 title: 4.1 TASK 1 – Program Management and Administration
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 content length: 5012 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 title: 4.2 TASK 2 – Information Management
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 content length: 4727 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 title: 4.3 TASK 3 – Program Compliance
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 content length: 4871 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 4 title: 4.4 TASK 4 – Training and Outreach
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 4 content length: 5589 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 5 title: 4.5 TASK 5 – Regulatory Support
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 5 content length: 4716 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 6 title: 4.6 TASK 6 – Optional – Surge
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 6 content length: 4250 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing section 5: <class 'dict'>
2025-08-03 17:24:28 | INFO     | VALIDATION | Section title: 5.0 Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-03 17:24:28 | INFO     | VALIDATION | Section content length: 5148 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions delivers consistently high-quality systems engineering and technical assistance (SETA) support to Department of Defense (DoD) and civilian agencies. Our approach centers on...
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing 3 subsections
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 title: 5.1 Experience Example 1
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 1 content length: 4566 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 title: 5.2 Experience Example 2
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 2 content length: 5605 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 title: 5.3 Experience Example 3
2025-08-03 17:24:28 | INFO     | VALIDATION | Subsection 3 content length: 5064 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Generated markdown with 100197 characters
2025-08-03 17:24:28 | INFO     | VALIDATION | Checking for TOC: opportunity_details=True, has_toc_text=True
2025-08-03 17:24:28 | INFO     | VALIDATION | TOC text found: [{"title": "Tab A - Proposal Cover/Transmittal Letter", "description": "Standard cover letter for th...
2025-08-03 17:24:28 | INFO     | VALIDATION | TOC JSON parsed successfully, 5 items
2025-08-03 17:24:28 | INFO     | VALIDATION | Content generated (length: 100197)
2025-08-03 17:24:28 | INFO     | VALIDATION | Using compliance formatting: margin=50pts, font=Times-Roman, body_size=12, header_size=14, footer_size=10, line_spacing=1.5
2025-08-03 17:24:28 | INFO     | VALIDATION | Processing cover page with 1 elements
2025-08-03 17:24:28 | INFO     | VALIDATION | Created templates for cover page and regular pages
2025-08-03 17:24:28 | INFO     | VALIDATION | Adding 1 cover page elements to PDF
2025-08-03 17:24:28 | INFO     | VALIDATION | Cover page added
2025-08-03 17:24:28 | INFO     | VALIDATION | Generating TOC with accurate page numbers
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 885 lines of markdown content
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Personnel Name**', '**Role**', '**Education**', '**Relevant Experience (Years)**', '**Key Skills**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Eleanor Vance', 'Project Manager', 'MS, Engineering Management, Stanford U.', '12', 'PMP, Agile methodologies, Risk Management, Stakeholder Communication']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['David Sterling', 'Lead Systems Engineer', 'MS, Systems Engineering, MIT', '10', 'DOORS, SysML, Model-Based Systems Engineering (MBSE), Requirements Analysis']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Anya Sharma', 'Data Scientist', 'PhD, Statistics, Johns Hopkins U.', '7', 'Python, R, Machine Learning, Data Visualization, Statistical Modeling']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Ben Carter', 'Cybersecurity Specialist', 'CISSP, BS, Computer Science, UMD', '6', 'Penetration Testing, Vulnerability Assessment, Security Auditing, NIST Framework']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill', '< 60 days', 'Monthly']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cost Per Hire', '< $5,000', 'Quarterly']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '< 10%', 'Annually']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction Score', '> 4.0 (out of 5)', 'Semi-Annually']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Offer Acceptance Rate', '> 90%', 'Quarterly']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 6 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 6 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 6 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 6 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Training Area**', '**Frequency**', '**Delivery Method**', '**Measurement Metric**', '**Target Outcome**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cybersecurity Awareness', 'Annually', 'Online Module & Simulated Phishing', 'Completion Rate > 95%, Phishing Click Rate < 5%', 'Reduced security incidents']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project Management Best Practices', 'Bi-Annually', 'Instructor-Led Training', 'Project Schedule Adherence > 90%, Budget Variance < 10%', 'Improved project delivery']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Section 508 Compliance', 'Bi-Annually', 'Online Module & Practical Exercise', '100% Compliance with Section 508 Standards', 'Accessible deliverables']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Agile Methodologies', 'Quarterly', 'Instructor-Led Workshop', 'Team Velocity Increase > 10%', 'Increased team productivity']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Role**', '**Name**', '**Responsibilities**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Program Manager', 'Eleanor Vance', 'Overall project leadership, client communication, risk management, and resource allocation.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technical Lead', 'David Chen', 'Technical direction, solution design, and oversight of technical deliverables.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Quality Assurance Manager', 'Maria Rodriguez', 'Development and implementation of quality assurance processes, testing, and defect management.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Subject Matter Expert (SME)', 'Samuel Okoro', 'Providing specialized expertise in [Specific Technical Area relevant to RFP], ensuring technical accuracy and compliance.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Risk**', '**Mitigation Strategy**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Delays in Government approvals', 'Proactive communication and early submission of deliverables for review.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technical challenges with [Specific Technology]', 'Employing experienced SMEs and conducting thorough research and prototyping.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Resource constraints', 'Maintaining a pool of qualified personnel and cross-training team members.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Baseline (Q1)', 'Target (Q4)', 'Measurement Frequency', 'Data Source']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '15%', '10%', 'Quarterly', 'HR Records']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Engagement Score', '70%', '80%', 'Quarterly', 'Employee Surveys']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill (Critical)', '60 days', '45 days', 'Monthly', 'HR Records']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cost of Turnover', '$50,000', '$40,000', 'Annually', 'Finance & HR Data']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Skill Category', 'Junior (FTE)', 'Mid-Level (FTE)', 'Senior (FTE)', 'Architect (FTE)', 'Total Available FTE']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Systems Engineering', '8', '12', '6', '2', '28']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cybersecurity', '5', '8', '4', '1', '18']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Analytics', '6', '10', '5', '2', '23']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cloud Infrastructure', '7', '9', '6', '3', '25']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Total**', '**26**', '**39**', '**21**', '**8**', '**94**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 6 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 6 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 6 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 6 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**KPI**', '**Measurement Method**', '**Target Value**', '**Reporting Frequency**', '**Corrective Action Trigger**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Defect Density', 'Number of defects per 1,000 lines of code/document pages', '< 2.0', 'Bi-weekly', '> 2.0 – Root cause analysis & process improvement']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable On-Time Rate', 'Percentage of deliverables submitted on schedule', '> 95%', 'Bi-weekly', '< 95% – Schedule review & resource reallocation']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Requirements Traceability', 'Percentage of requirements covered by test cases', '100%', 'Monthly', '< 100% – Test case development & requirements clarification']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction Score', 'Post-deliverable survey (scale of 1-5)', '> 4.0', 'Post-Deliverable', '< 4.0 – Issue resolution & process adjustment']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Test Type', 'Description', 'Tools Used', 'Entry Criteria', 'Exit Criteria']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Unit Testing', 'Testing individual software components in isolation.', 'JUnit, pytest', 'Code completion for individual components.', '90% code coverage, all tests pass.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Integration Testing', 'Testing the interaction between different software components.', 'Postman, SoapUI', 'Successful unit testing of components.', 'All integration tests pass.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['System Testing', 'Testing the entire system to ensure it meets all requirements.', 'Selenium, JMeter', 'Successful integration testing.', 'All system tests pass, performance targets met.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['User Acceptance Testing (UAT)', 'Testing by end-users to validate the system meets their needs.', 'UAT Test Scripts', 'Successful system testing.', 'User sign-off on UAT test results.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Risk Category', 'Potential Risk', 'Mitigation Strategy', 'Probability', 'Impact', 'Severity']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Technical**', 'Unexpected technical challenges', 'Dedicated SME support, proactive research & development', 'Medium', 'High', 'High']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Schedule**', 'Delays in deliverable completion', 'Detailed schedule management, resource leveling, contingency planning', 'Medium', 'Medium', 'Medium']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Resource**', 'Key personnel unavailability', 'Cross-training, resource backup plans, proactive staffing', 'Low', 'Medium', 'Low']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target Threshold', 'Measurement Frequency', 'Reporting Frequency']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Accuracy Rate', '99.9%', 'Daily', 'Weekly']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Completeness Rate', '99.5%', 'Daily', 'Weekly']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Consistency Rate', '99.8%', 'Weekly', 'Monthly']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Metric**', '**Description**', '**Target**', '**Reporting Frequency**', '**Deliverable**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Number of Non-Compliance Issues**', 'Total number of identified non-compliance issues.', '< 3 per month', 'Monthly Compliance Report', 'Monthly Compliance Report']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**CAPA Closure Rate**', 'Percentage of CAPA items closed within the defined timeframe.', '> 95%', 'Monthly Compliance Report', 'Monthly Compliance Report']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Audit Findings Resolution Time**', 'Average time to resolve audit findings.', '< 14 days', 'Monthly Compliance Report', 'Monthly Compliance Report']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Training Completion Rate**', 'Percentage of personnel completing required compliance training.', '> 90%', 'Monthly Compliance Report', 'Training Records']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target Value', 'Measurement Frequency', 'Data Source']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '90%', 'Monthly', 'Learning Management System']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['User Satisfaction (Training)', '4.5/5', 'Post-Training Survey', 'Survey Results']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['System Adoption Rate', '80%', 'Quarterly', 'System Usage Data']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Help Desk Ticket Volume', '10% Reduction', 'Monthly', 'Help Desk System']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Regulatory Area', 'Relevant Standards/Regulations', 'Adept Experience (Years)', 'Key Personnel']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Environmental Compliance', 'NEPA, Clean Water Act, Clean Air Act, RCRA', '15+', 'Dr. Eleanor Vance (Certified Environmental Professional)']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Safety & Health', 'OSHA 29 CFR 1910, ANSI Z10', '10+', 'Mr. David Chen (Certified Safety Professional)']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Quality Assurance', 'ISO 9001, ANSI/ASQ Z1.4', '8+', 'Ms. Sarah Johnson (Six Sigma Black Belt)']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Security', 'FISMA, NIST 800-53', '5+', 'Mr. Michael Rodriguez (Certified Information Systems Security Professional)']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Scalability Component**', '**Description**', '**Implementation Tool**', '**Measurable Outcome**']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Automated Provisioning**', 'Automated creation of user accounts, system access, and virtual machines.', 'Terraform, Ansible', 'Reduction in provisioning time from 48 hours to 4 hours.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Cloud-Based Infrastructure**', 'Leveraging AWS GovCloud for scalable compute, storage, and networking resources.', 'AWS GovCloud', 'Ability to scale resources up or down by 50% within 2 hours.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Virtual Desktop Infrastructure (VDI)**', 'Providing secure remote access to applications and data via VDI.', 'VMware Horizon', 'Support for up to 200 concurrent remote users with minimal performance degradation.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Load Balancing**', 'Distributing workload across multiple servers to prevent bottlenecks.', 'NGINX, HAProxy', 'Maintain system uptime of 99.9% during peak load.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project Name', 'Agency', 'Project Duration', 'Key Capabilities Demonstrated', 'Measurable Outcome']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Integrated Sensor Network Development', 'US Army', '36 Months', 'Systems Engineering, Data Analytics, Cybersecurity, Network Integration', 'Delivered a fully operational sensor network with a 20% improvement in target detection accuracy.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Logistics Data Management System Upgrade', 'Department of Navy', '24 Months', 'Database Management, Software Development, System Integration, Data Migration', 'Successfully migrated a legacy database to a modern platform, resulting in a 30% reduction in data processing time.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cybersecurity Vulnerability Assessment', 'Department of Homeland Security', '12 Months', 'Penetration Testing, Vulnerability Analysis, Risk Assessment, Security Hardening', 'Identified and mitigated 15 critical vulnerabilities, significantly improving the security posture of the target system.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Command and Control System Modernization', 'US Air Force', '18 Months', 'Software Engineering, User Interface/User Experience (UI/UX) Design, System Integration, Testing', 'Delivered a modernized command and control system with improved usability and functionality, resulting in a 15% increase in operator efficiency.']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable', 'Description', 'Completion Date']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Sensor Calibration Report', 'Detailed report documenting the calibration procedure and results.', 'June 2021']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Fusion Algorithm Design Document', 'Comprehensive document outlining the design and implementation of the EKF.', 'September 2021']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Integrated Prototype System', 'Fully functional prototype integrated into the UGV platform.', 'March 2022']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Test & Evaluation Report', 'Report documenting the results of extensive field testing.', 'June 2023']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Baseline (Pre-Implementation)', 'Post-Implementation', 'Improvement']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Intrusion Detection Rate', '65%', '98%', '33%']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['False Positive Rate', '20%', '2%', '18%']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Average Response Time', '15 minutes', '5 minutes', '10 minutes']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Perimeter Coverage', '70%', '95%', '25%']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technology/Tool', 'Application', 'Specific Benefit']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Radar (Frequency Modulated Continuous Wave)**', 'Perimeter intrusion detection', 'Long-range detection, all-weather operation, minimal false alarms']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Thermal Cameras (LWIR)**', 'Target identification & classification', 'Detection of heat signatures, day/night operation, foliage penetration']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Acoustic Sensors (Microphone Arrays)**', 'Sound event detection & localization', 'Detection of footsteps, vehicles, and other sounds, directional information']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Buried Fiber Optic Cables (Distributed Acoustic Sensing - DAS)**', 'Ground vibration detection', 'Detection of footsteps, digging, and vehicle movement, high sensitivity']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Python (with TensorFlow & Scikit-learn)**', 'Data analytics & machine learning', 'Rapid prototyping, algorithm development, data visualization']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Secure Communication Protocols (TLS/SSL)**', 'Data transmission & security', 'Encryption of data in transit, protection against unauthorized access']
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Ending table with 7 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Adding table with 7 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Processing 7 cleaned table rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Successfully added table with 7 rows
2025-08-03 17:24:28 | INFO     | VALIDATION | PDF Generator: Generated 506 ReportLab elements
2025-08-03 17:24:29 | INFO     | VALIDATION | Calculated 41 pages for main content
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC entry: 1.0 Tab A - Proposal Cover/Transmittal Letter at page 3
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC entry: 2.0 Tab B - Factor 1 - Staffing & Key Personnel Qualifications at page 11
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 2.1 Recruitment, Hiring, and Retention Approach at page 12
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 2.2 Certifications and Training Processes at page 13
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 2.3 Resume of Proposed Key Personnel at page 14
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 2.4 Tentative/Contingent Offer Letter at page 15
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC entry: 3.0 Tab C - Factor 2 - Management Approach at page 19
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 3.1 Employee Turnover and Solutions at page 20
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 3.2 Surge Support Availability at page 21
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 3.3 Quality Control and Performance Monitoring at page 22
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC entry: 4.0 Tab D - Factor 3 - Technical Approach at page 27
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 4.1 TASK 1 – Program Management and Administration at page 28
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 4.2 TASK 2 – Information Management at page 29
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 4.3 TASK 3 – Program Compliance at page 30
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 4.4 TASK 4 – Training and Outreach at page 31
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 4.5 TASK 5 – Regulatory Support at page 32
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 4.6 TASK 6 – Optional – Surge at page 33
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC entry: 5.0 Tab E - Factor 4 - Demonstrated Corporate Experience at page 35
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 5.1 Experience Example 1 at page 36
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 5.2 Experience Example 2 at page 37
2025-08-03 17:24:29 | INFO     | VALIDATION | Added TOC subsection: 5.3 Experience Example 3 at page 38
2025-08-03 17:24:29 | INFO     | VALIDATION | Generated TOC with accurate page numbers using two-pass approach
2025-08-03 17:24:29 | INFO     | VALIDATION | TOC added after cover page
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 885 lines of markdown content
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Personnel Name**', '**Role**', '**Education**', '**Relevant Experience (Years)**', '**Key Skills**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Eleanor Vance', 'Project Manager', 'MS, Engineering Management, Stanford U.', '12', 'PMP, Agile methodologies, Risk Management, Stakeholder Communication']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['David Sterling', 'Lead Systems Engineer', 'MS, Systems Engineering, MIT', '10', 'DOORS, SysML, Model-Based Systems Engineering (MBSE), Requirements Analysis']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Anya Sharma', 'Data Scientist', 'PhD, Statistics, Johns Hopkins U.', '7', 'Python, R, Machine Learning, Data Visualization, Statistical Modeling']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Ben Carter', 'Cybersecurity Specialist', 'CISSP, BS, Computer Science, UMD', '6', 'Penetration Testing, Vulnerability Assessment, Security Auditing, NIST Framework']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill', '< 60 days', 'Monthly']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cost Per Hire', '< $5,000', 'Quarterly']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '< 10%', 'Annually']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction Score', '> 4.0 (out of 5)', 'Semi-Annually']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Offer Acceptance Rate', '> 90%', 'Quarterly']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 6 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 6 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 6 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 6 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Training Area**', '**Frequency**', '**Delivery Method**', '**Measurement Metric**', '**Target Outcome**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cybersecurity Awareness', 'Annually', 'Online Module & Simulated Phishing', 'Completion Rate > 95%, Phishing Click Rate < 5%', 'Reduced security incidents']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project Management Best Practices', 'Bi-Annually', 'Instructor-Led Training', 'Project Schedule Adherence > 90%, Budget Variance < 10%', 'Improved project delivery']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Section 508 Compliance', 'Bi-Annually', 'Online Module & Practical Exercise', '100% Compliance with Section 508 Standards', 'Accessible deliverables']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Agile Methodologies', 'Quarterly', 'Instructor-Led Workshop', 'Team Velocity Increase > 10%', 'Increased team productivity']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Role**', '**Name**', '**Responsibilities**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Program Manager', 'Eleanor Vance', 'Overall project leadership, client communication, risk management, and resource allocation.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technical Lead', 'David Chen', 'Technical direction, solution design, and oversight of technical deliverables.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Quality Assurance Manager', 'Maria Rodriguez', 'Development and implementation of quality assurance processes, testing, and defect management.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Subject Matter Expert (SME)', 'Samuel Okoro', 'Providing specialized expertise in [Specific Technical Area relevant to RFP], ensuring technical accuracy and compliance.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Risk**', '**Mitigation Strategy**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Delays in Government approvals', 'Proactive communication and early submission of deliverables for review.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technical challenges with [Specific Technology]', 'Employing experienced SMEs and conducting thorough research and prototyping.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Resource constraints', 'Maintaining a pool of qualified personnel and cross-training team members.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Baseline (Q1)', 'Target (Q4)', 'Measurement Frequency', 'Data Source']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '15%', '10%', 'Quarterly', 'HR Records']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Engagement Score', '70%', '80%', 'Quarterly', 'Employee Surveys']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill (Critical)', '60 days', '45 days', 'Monthly', 'HR Records']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cost of Turnover', '$50,000', '$40,000', 'Annually', 'Finance & HR Data']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Skill Category', 'Junior (FTE)', 'Mid-Level (FTE)', 'Senior (FTE)', 'Architect (FTE)', 'Total Available FTE']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Systems Engineering', '8', '12', '6', '2', '28']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cybersecurity', '5', '8', '4', '1', '18']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Analytics', '6', '10', '5', '2', '23']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cloud Infrastructure', '7', '9', '6', '3', '25']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Total**', '**26**', '**39**', '**21**', '**8**', '**94**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 6 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 6 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 6 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 6 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**KPI**', '**Measurement Method**', '**Target Value**', '**Reporting Frequency**', '**Corrective Action Trigger**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Defect Density', 'Number of defects per 1,000 lines of code/document pages', '< 2.0', 'Bi-weekly', '> 2.0 – Root cause analysis & process improvement']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable On-Time Rate', 'Percentage of deliverables submitted on schedule', '> 95%', 'Bi-weekly', '< 95% – Schedule review & resource reallocation']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Requirements Traceability', 'Percentage of requirements covered by test cases', '100%', 'Monthly', '< 100% – Test case development & requirements clarification']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction Score', 'Post-deliverable survey (scale of 1-5)', '> 4.0', 'Post-Deliverable', '< 4.0 – Issue resolution & process adjustment']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Test Type', 'Description', 'Tools Used', 'Entry Criteria', 'Exit Criteria']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Unit Testing', 'Testing individual software components in isolation.', 'JUnit, pytest', 'Code completion for individual components.', '90% code coverage, all tests pass.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Integration Testing', 'Testing the interaction between different software components.', 'Postman, SoapUI', 'Successful unit testing of components.', 'All integration tests pass.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['System Testing', 'Testing the entire system to ensure it meets all requirements.', 'Selenium, JMeter', 'Successful integration testing.', 'All system tests pass, performance targets met.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['User Acceptance Testing (UAT)', 'Testing by end-users to validate the system meets their needs.', 'UAT Test Scripts', 'Successful system testing.', 'User sign-off on UAT test results.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Risk Category', 'Potential Risk', 'Mitigation Strategy', 'Probability', 'Impact', 'Severity']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Technical**', 'Unexpected technical challenges', 'Dedicated SME support, proactive research & development', 'Medium', 'High', 'High']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Schedule**', 'Delays in deliverable completion', 'Detailed schedule management, resource leveling, contingency planning', 'Medium', 'Medium', 'Medium']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Resource**', 'Key personnel unavailability', 'Cross-training, resource backup plans, proactive staffing', 'Low', 'Medium', 'Low']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target Threshold', 'Measurement Frequency', 'Reporting Frequency']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Accuracy Rate', '99.9%', 'Daily', 'Weekly']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Completeness Rate', '99.5%', 'Daily', 'Weekly']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Consistency Rate', '99.8%', 'Weekly', 'Monthly']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Metric**', '**Description**', '**Target**', '**Reporting Frequency**', '**Deliverable**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Number of Non-Compliance Issues**', 'Total number of identified non-compliance issues.', '< 3 per month', 'Monthly Compliance Report', 'Monthly Compliance Report']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**CAPA Closure Rate**', 'Percentage of CAPA items closed within the defined timeframe.', '> 95%', 'Monthly Compliance Report', 'Monthly Compliance Report']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Audit Findings Resolution Time**', 'Average time to resolve audit findings.', '< 14 days', 'Monthly Compliance Report', 'Monthly Compliance Report']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Training Completion Rate**', 'Percentage of personnel completing required compliance training.', '> 90%', 'Monthly Compliance Report', 'Training Records']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target Value', 'Measurement Frequency', 'Data Source']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '90%', 'Monthly', 'Learning Management System']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['User Satisfaction (Training)', '4.5/5', 'Post-Training Survey', 'Survey Results']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['System Adoption Rate', '80%', 'Quarterly', 'System Usage Data']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Help Desk Ticket Volume', '10% Reduction', 'Monthly', 'Help Desk System']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Regulatory Area', 'Relevant Standards/Regulations', 'Adept Experience (Years)', 'Key Personnel']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Environmental Compliance', 'NEPA, Clean Water Act, Clean Air Act, RCRA', '15+', 'Dr. Eleanor Vance (Certified Environmental Professional)']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Safety & Health', 'OSHA 29 CFR 1910, ANSI Z10', '10+', 'Mr. David Chen (Certified Safety Professional)']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Quality Assurance', 'ISO 9001, ANSI/ASQ Z1.4', '8+', 'Ms. Sarah Johnson (Six Sigma Black Belt)']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Security', 'FISMA, NIST 800-53', '5+', 'Mr. Michael Rodriguez (Certified Information Systems Security Professional)']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Scalability Component**', '**Description**', '**Implementation Tool**', '**Measurable Outcome**']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Automated Provisioning**', 'Automated creation of user accounts, system access, and virtual machines.', 'Terraform, Ansible', 'Reduction in provisioning time from 48 hours to 4 hours.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Cloud-Based Infrastructure**', 'Leveraging AWS GovCloud for scalable compute, storage, and networking resources.', 'AWS GovCloud', 'Ability to scale resources up or down by 50% within 2 hours.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Virtual Desktop Infrastructure (VDI)**', 'Providing secure remote access to applications and data via VDI.', 'VMware Horizon', 'Support for up to 200 concurrent remote users with minimal performance degradation.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Load Balancing**', 'Distributing workload across multiple servers to prevent bottlenecks.', 'NGINX, HAProxy', 'Maintain system uptime of 99.9% during peak load.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project Name', 'Agency', 'Project Duration', 'Key Capabilities Demonstrated', 'Measurable Outcome']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Integrated Sensor Network Development', 'US Army', '36 Months', 'Systems Engineering, Data Analytics, Cybersecurity, Network Integration', 'Delivered a fully operational sensor network with a 20% improvement in target detection accuracy.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Logistics Data Management System Upgrade', 'Department of Navy', '24 Months', 'Database Management, Software Development, System Integration, Data Migration', 'Successfully migrated a legacy database to a modern platform, resulting in a 30% reduction in data processing time.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cybersecurity Vulnerability Assessment', 'Department of Homeland Security', '12 Months', 'Penetration Testing, Vulnerability Analysis, Risk Assessment, Security Hardening', 'Identified and mitigated 15 critical vulnerabilities, significantly improving the security posture of the target system.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Command and Control System Modernization', 'US Air Force', '18 Months', 'Software Engineering, User Interface/User Experience (UI/UX) Design, System Integration, Testing', 'Delivered a modernized command and control system with improved usability and functionality, resulting in a 15% increase in operator efficiency.']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable', 'Description', 'Completion Date']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Sensor Calibration Report', 'Detailed report documenting the calibration procedure and results.', 'June 2021']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Fusion Algorithm Design Document', 'Comprehensive document outlining the design and implementation of the EKF.', 'September 2021']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Integrated Prototype System', 'Fully functional prototype integrated into the UGV platform.', 'March 2022']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Test & Evaluation Report', 'Report documenting the results of extensive field testing.', 'June 2023']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Baseline (Pre-Implementation)', 'Post-Implementation', 'Improvement']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Intrusion Detection Rate', '65%', '98%', '33%']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['False Positive Rate', '20%', '2%', '18%']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Average Response Time', '15 minutes', '5 minutes', '10 minutes']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Perimeter Coverage', '70%', '95%', '25%']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technology/Tool', 'Application', 'Specific Benefit']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Radar (Frequency Modulated Continuous Wave)**', 'Perimeter intrusion detection', 'Long-range detection, all-weather operation, minimal false alarms']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Thermal Cameras (LWIR)**', 'Target identification & classification', 'Detection of heat signatures, day/night operation, foliage penetration']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Acoustic Sensors (Microphone Arrays)**', 'Sound event detection & localization', 'Detection of footsteps, vehicles, and other sounds, directional information']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Buried Fiber Optic Cables (Distributed Acoustic Sensing - DAS)**', 'Ground vibration detection', 'Detection of footsteps, digging, and vehicle movement, high sensitivity']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Python (with TensorFlow & Scikit-learn)**', 'Data analytics & machine learning', 'Rapid prototyping, algorithm development, data visualization']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Secure Communication Protocols (TLS/SSL)**', 'Data transmission & security', 'Encryption of data in transit, protection against unauthorized access']
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Ending table with 7 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Adding table with 7 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Processing 7 cleaned table rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Successfully added table with 7 rows
2025-08-03 17:24:29 | INFO     | VALIDATION | PDF Generator: Generated 506 ReportLab elements
2025-08-03 17:24:29 | INFO     | VALIDATION | Main content added
2025-08-03 17:24:30 | INFO     | VALIDATION | Drew full-page cover image: /tmp/tmp7zk74uqt.png
2025-08-03 17:24:30 | INFO     | VALIDATION | Successfully drew text overlay on cover page
2025-08-03 17:24:30 | INFO     | VALIDATION | PDF file successfully saved to: /home/<USER>/Desktop/Development/NEW/GovBD-BackEnd-Python/AIService/generated-pdfs/RFP_Draft_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2_20250803_172428.pdf
2025-08-03 17:24:30 | INFO     | VALIDATION | Cleaned up temporary file: /tmp/tmp7zk74uqt.png
2025-08-03 17:25:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:25:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:25:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:25:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:25:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:25:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:26:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:26:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:26:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:26:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:26:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:26:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:27:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:27:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:27:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:27:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:27:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:27:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:28:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:28:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:28:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:28:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:28:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:28:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:29:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:29:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:29:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:29:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:29:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:29:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:30:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:30:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:30:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:30:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:30:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:30:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:31:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:31:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:31:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:31:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:31:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:31:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:32:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:32:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:32:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:32:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:32:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:32:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:33:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:33:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:33:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:33:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:33:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:33:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:34:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:34:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:34:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:34:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:34:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:34:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:35:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:35:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:35:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:35:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:35:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:35:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:36:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:36:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:36:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:36:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:36:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:36:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:37:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:37:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:37:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:37:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:37:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:37:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:38:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:38:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:38:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:38:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:38:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:38:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:39:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:39:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:39:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:39:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:39:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:39:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:40:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:40:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:40:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:40:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:40:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:40:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:41:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:41:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:41:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:41:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:41:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:41:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:42:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:42:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:42:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:42:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:42:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:42:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:43:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:43:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:43:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:43:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:43:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:43:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:44:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:44:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:44:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:44:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:44:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:44:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:45:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:45:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:45:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:45:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:45:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:45:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:46:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:46:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:46:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:46:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:46:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:46:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:47:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:47:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:47:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:47:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:47:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:47:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:48:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:48:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:48:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:48:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:48:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:48:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:49:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:49:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:49:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:49:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:49:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:49:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:50:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:50:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:50:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:50:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:50:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:50:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:51:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:51:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:51:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:51:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:51:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:51:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:52:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:52:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:52:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:52:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:52:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:52:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:53:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:53:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:53:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:53:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:53:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:53:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:54:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:54:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:54:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:54:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:54:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:54:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:55:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:55:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:55:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:55:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:55:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:55:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:56:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:56:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:56:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:56:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:56:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:56:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:57:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:57:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:57:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:57:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:57:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:57:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:58:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:58:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:58:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:58:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:58:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:58:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 17:59:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 17:59:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 17:59:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 17:59:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 17:59:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 17:59:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:00:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:00:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:00:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:00:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:00:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:00:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:01:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:01:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:01:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:01:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:01:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:01:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:02:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:02:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:02:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:02:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:02:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:02:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:03:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:03:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:03:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:03:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:03:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:03:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:04:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:04:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:04:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:04:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:04:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:04:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:05:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:05:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:05:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:05:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:05:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:05:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-03 18:06:22 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-03 18:06:22 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-03 18:06:22 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-03 18:06:22 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-03 18:06:22 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-03 18:06:22 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
