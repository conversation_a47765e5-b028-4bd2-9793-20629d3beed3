#!/usr/bin/env python3
"""
Demo Script for Enhanced Proposal Outline Generation

This script demonstrates the complete workflow:
1. Generate Table of Contents with government compliance validation
2. Create detailed outlines for each section
3. Save results with performance metrics and compliance reports

Usage:
    python demo_outline_generation.py
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.proposal.outline import ProposalOutlineService
from services.proposal.config.outline_config import get_config_by_environment
from loguru import logger

# Configure logging
logger.add("outline_generation_demo.log", rotation="10 MB", level="INFO")


class OutlineGenerationDemo:
    """Demo class for proposal outline generation workflow."""
    
    def __init__(self, environment: str = "production"):
        """Initialize the demo with specified environment configuration."""
        self.config = get_config_by_environment(environment)
        self.outline_service = ProposalOutlineService(
            embedding_api_url=self.config.embedding_api_url,
            llm_api_url=self.config.llm_api_url
        )
        self.results_dir = Path("demo_results")
        self.results_dir.mkdir(exist_ok=True)
        
    async def run_complete_demo(self):
        """Run the complete outline generation demo."""
        logger.info("🚀 Starting Enhanced Proposal Outline Generation Demo")

        # Sample government RFP data
        demo_data = self._get_demo_rfp_data()

        try:
            # Step 1: Generate Table of Contents
            logger.info("📋 Step 1: Generating Table of Contents...")
            toc_result = await self._generate_table_of_contents(demo_data)

            # Step 2: Generate Detailed Outlines
            logger.info("📝 Step 2: Generating Detailed Outlines...")
            outline_result = await self._generate_detailed_outlines(demo_data, toc_result)

            # Step 3: Generate Draft Content (Optional)
            logger.info("✍️  Step 3: Generating Draft Content...")
            draft_result = await self._generate_draft_content(demo_data, toc_result)

            # Step 4: Save Results
            logger.info("💾 Step 4: Saving Results...")
            await self._save_results(demo_data, toc_result, outline_result, draft_result)

            # Step 5: Generate Summary Report
            logger.info("📊 Step 5: Generating Summary Report...")
            await self._generate_summary_report(toc_result, outline_result, draft_result)

            logger.info("✅ Demo completed successfully!")

        except Exception as e:
            logger.error(f"❌ Demo failed: {str(e)}")
            raise

    def _get_demo_rfp_data(self) -> dict:
        """Get sample RFP data for demonstration."""
        return {
            "opportunity_id": "DEMO_RFP_2024_001",
            "tenant_id": "demo_tenant",
            "source": "custom",
            "is_rfp": True,
            "volume_information": """
            VOLUME I - TECHNICAL PROPOSAL
            
            The Government requires a comprehensive technical proposal that demonstrates the Offeror's 
            understanding of the requirements and technical approach. The proposal shall be organized 
            as follows:
            
            1.0 TECHNICAL APPROACH
            2.0 PAST PERFORMANCE 
            3.0 MANAGEMENT APPROACH
            4.0 STAFFING PLAN
            5.0 QUALITY ASSURANCE PLAN
            
            Proposals shall follow government numbering conventions and not exceed 25 pages.
            """,
            "content_compliance": """
            STATEMENT OF WORK (SOW)
            
            Task 1: System Analysis and Requirements Gathering
            - Conduct comprehensive analysis of current systems
            - Document functional and technical requirements
            - Deliver requirements specification document
            
            Task 2: Solution Design and Architecture
            - Develop system architecture and design
            - Create technical specifications
            - Deliver design documentation
            
            Task 3: Implementation and Integration
            - Implement proposed solution
            - Integrate with existing systems
            - Conduct system testing
            
            Task 4: Training and Knowledge Transfer
            - Develop training materials
            - Conduct user training sessions
            - Provide ongoing support documentation
            
            EVALUATION CRITERIA:
            - Technical Approach (40%)
            - Past Performance (30%)
            - Management Approach (20%)
            - Staffing Plan (10%)
            
            EXPERIENCE REQUIREMENTS:
            Offerors must demonstrate at least 3 relevant project experiences within the last 5 years
            involving similar system implementations for government agencies.
            """
        }

    async def _generate_table_of_contents(self, demo_data: dict) -> dict:
        """Generate table of contents with timing and validation."""
        start_time = time.time()
        
        try:
            result = await self.outline_service.generate_table_of_contents(
                opportunity_id=demo_data["opportunity_id"],
                tenant_id=demo_data["tenant_id"],
                source=demo_data["source"],
                volume_information=demo_data["volume_information"],
                content_compliance=demo_data["content_compliance"],
                is_rfp=demo_data["is_rfp"]
            )
            
            generation_time = time.time() - start_time
            
            # Parse and validate the generated TOC
            try:
                toc_data = json.loads(result["content"])
                logger.info(f"✅ TOC generated successfully in {generation_time:.2f}s")
                
                if "compliance_report" in result:
                    compliance = result["compliance_report"]
                    logger.info(f"📊 Compliance Score: {compliance['compliance_score']:.1f}%")
                    logger.info(f"🔍 Issues Found: {compliance['issues_count']}")
                    
                    if compliance["recommendations"]:
                        logger.info("💡 Top Recommendations:")
                        for rec in compliance["recommendations"]:
                            logger.info(f"   - {rec}")
                
                return {
                    "toc_data": toc_data,
                    "generation_time": generation_time,
                    "compliance_report": result.get("compliance_report"),
                    "raw_result": result
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ Failed to parse TOC JSON: {e}")
                return {
                    "toc_data": None,
                    "generation_time": generation_time,
                    "error": str(e),
                    "raw_result": result
                }
                
        except Exception as e:
            generation_time = time.time() - start_time
            logger.error(f"❌ TOC generation failed after {generation_time:.2f}s: {e}")
            raise

    async def _generate_detailed_outlines(self, demo_data: dict, toc_result: dict) -> dict:
        """Generate detailed outlines for each section."""
        if not toc_result.get("toc_data") or "table_of_contents" not in toc_result["toc_data"]:
            logger.error("❌ Cannot generate outlines: Invalid TOC data")
            return {"error": "Invalid TOC data"}
        
        start_time = time.time()
        
        try:
            table_of_contents = toc_result["toc_data"]["table_of_contents"]
            
            result = await self.outline_service.generate_outline(
                opportunity_id=demo_data["opportunity_id"],
                tenant_id=demo_data["tenant_id"],
                source=demo_data["source"],
                table_of_contents=table_of_contents
            )
            
            generation_time = time.time() - start_time
            
            logger.info(f"✅ Detailed outlines generated in {generation_time:.2f}s")
            logger.info(f"📄 Generated {len(result.get('outlines', []))} section outlines")
            
            return {
                "outlines": result.get("outlines", []),
                "generation_time": generation_time,
                "sections_count": len(result.get("outlines", [])),
                "raw_result": result
            }
            
        except Exception as e:
            generation_time = time.time() - start_time
            logger.error(f"❌ Outline generation failed after {generation_time:.2f}s: {e}")
            raise

    async def _generate_draft_content(self, demo_data: dict, toc_result: dict) -> dict:
        """Generate draft content for the first few sections."""
        if not toc_result.get("toc_data") or "table_of_contents" not in toc_result["toc_data"]:
            logger.warning("⚠️  Skipping draft generation: Invalid TOC data")
            return {"error": "Invalid TOC data", "drafts": []}

        start_time = time.time()

        try:
            table_of_contents = toc_result["toc_data"]["table_of_contents"]

            # Generate drafts for first 2 sections only (for demo purposes)
            limited_toc = table_of_contents[:2]

            logger.info(f"Generating drafts for {len(limited_toc)} sections (limited for demo)")

            result = await self.outline_service.generate_draft(
                opportunity_id=demo_data["opportunity_id"],
                tenant_id=demo_data["tenant_id"],
                source=demo_data["source"],
                client_short_name="demo_client",
                tenant_metadata="Demo Company - Professional Services Provider specializing in government consulting and system implementations.",
                table_of_contents=limited_toc
            )

            generation_time = time.time() - start_time

            logger.info(f"✅ Draft content generated in {generation_time:.2f}s")
            logger.info(f"📄 Generated {len(result.get('draft', []))} section drafts")

            return {
                "drafts": result.get("draft", []),
                "generation_time": generation_time,
                "sections_count": len(result.get("draft", [])),
                "raw_result": result
            }

        except Exception as e:
            generation_time = time.time() - start_time
            logger.warning(f"⚠️  Draft generation failed after {generation_time:.2f}s: {e}")
            return {
                "error": str(e),
                "generation_time": generation_time,
                "drafts": []
            }

    async def _save_results(self, demo_data: dict, toc_result: dict, outline_result: dict, draft_result: dict = None):
        """Save all results to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save Table of Contents
        toc_file = self.results_dir / f"table_of_contents_{timestamp}.json"
        with open(toc_file, 'w', encoding='utf-8') as f:
            json.dump({
                "metadata": {
                    "opportunity_id": demo_data["opportunity_id"],
                    "generated_at": datetime.now().isoformat(),
                    "generation_time": toc_result.get("generation_time"),
                    "compliance_report": toc_result.get("compliance_report")
                },
                "table_of_contents": toc_result.get("toc_data")
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 TOC saved to: {toc_file}")
        
        # Save Detailed Outlines
        outline_file = self.results_dir / f"detailed_outlines_{timestamp}.json"
        with open(outline_file, 'w', encoding='utf-8') as f:
            json.dump({
                "metadata": {
                    "opportunity_id": demo_data["opportunity_id"],
                    "generated_at": datetime.now().isoformat(),
                    "generation_time": outline_result.get("generation_time"),
                    "sections_count": outline_result.get("sections_count")
                },
                "outlines": outline_result.get("outlines", [])
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Outlines saved to: {outline_file}")
        
        # Save Demo Data for Reference
        demo_file = self.results_dir / f"demo_data_{timestamp}.json"
        with open(demo_file, 'w', encoding='utf-8') as f:
            json.dump(demo_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Demo data saved to: {demo_file}")

        # Save Draft Content if available
        if draft_result and draft_result.get("drafts"):
            draft_file = self.results_dir / f"draft_content_{timestamp}.json"
            with open(draft_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "metadata": {
                        "opportunity_id": demo_data["opportunity_id"],
                        "generated_at": datetime.now().isoformat(),
                        "generation_time": draft_result.get("generation_time"),
                        "sections_count": draft_result.get("sections_count")
                    },
                    "drafts": draft_result.get("drafts", [])
                }, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Draft content saved to: {draft_file}")

    async def _generate_summary_report(self, toc_result: dict, outline_result: dict, draft_result: dict = None):
        """Generate a comprehensive summary report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.results_dir / f"generation_report_{timestamp}.md"
        
        total_time = toc_result.get("generation_time", 0) + outline_result.get("generation_time", 0)
        
        report_content = f"""# Proposal Outline Generation Report

## Summary
- **Generated At**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Total Generation Time**: {total_time:.2f} seconds
- **Sections Generated**: {outline_result.get("sections_count", 0)}

## Table of Contents Generation
- **Time**: {toc_result.get("generation_time", 0):.2f} seconds
- **Status**: {"✅ Success" if toc_result.get("toc_data") else "❌ Failed"}
"""
        
        # Add compliance information if available
        if toc_result.get("compliance_report"):
            compliance = toc_result["compliance_report"]
            report_content += f"""
## Compliance Validation
- **Compliance Score**: {compliance.get("compliance_score", 0):.1f}%
- **Issues Found**: {compliance.get("issues_count", 0)}
- **Status**: {"✅ Compliant" if compliance.get("is_compliant") else "⚠️ Issues Found"}

### Recommendations
"""
            for rec in compliance.get("recommendations", []):
                report_content += f"- {rec}\n"
        
        report_content += f"""
## Detailed Outlines Generation
- **Time**: {outline_result.get("generation_time", 0):.2f} seconds
- **Sections**: {outline_result.get("sections_count", 0)}
- **Status**: {"✅ Success" if outline_result.get("outlines") else "❌ Failed"}

## Performance Metrics
- **Average Time per Section**: {(outline_result.get("generation_time", 0) / max(outline_result.get("sections_count", 1), 1)):.2f} seconds
- **Total Processing Time**: {total_time:.2f} seconds

## Files Generated
- Table of Contents: `table_of_contents_{timestamp}.json`
- Detailed Outlines: `detailed_outlines_{timestamp}.json`
- Demo Data: `demo_data_{timestamp}.json`
- This Report: `generation_report_{timestamp}.md`
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📊 Summary report saved to: {report_file}")
        
        # Print summary to console
        print("\n" + "="*60)
        print("🎉 PROPOSAL OUTLINE GENERATION DEMO COMPLETE")
        print("="*60)
        print(f"📋 Table of Contents: {toc_result.get('generation_time', 0):.2f}s")
        print(f"📝 Detailed Outlines: {outline_result.get('generation_time', 0):.2f}s")
        print(f"⏱️  Total Time: {total_time:.2f}s")
        print(f"📄 Sections Generated: {outline_result.get('sections_count', 0)}")
        
        if toc_result.get("compliance_report"):
            compliance = toc_result["compliance_report"]
            print(f"📊 Compliance Score: {compliance.get('compliance_score', 0):.1f}%")
        
        print(f"💾 Results saved in: {self.results_dir}")
        print("="*60)


async def main():
    """Main function to run the demo."""
    print("🚀 Enhanced Proposal Outline Generation Demo")
    print("=" * 50)
    
    # You can change the environment here: "development", "production", "government"
    environment = "production"
    
    demo = OutlineGenerationDemo(environment=environment)
    
    try:
        await demo.run_complete_demo()
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
        print("\n⏹️  Demo stopped by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
