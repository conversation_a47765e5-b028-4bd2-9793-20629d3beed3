"""
Configuration settings for the enhanced proposal outline generation system.

This module contains optimized settings for government proposal generation,
including LLM parameters, performance tuning, and validation thresholds.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass
class LLMConfig:
    """Configuration for LLM model parameters."""
    model: str = "gemma3:27b"
    num_ctx: int = 12000  # Increased context window for complex proposals
    temperature: float = 0.1  # Slight creativity while maintaining consistency
    num_predict: int = 4096  # Increased output length
    top_p: float = 0.9  # Nucleus sampling
    repeat_penalty: float = 1.1  # Reduce repetition
    timeout: int = 120  # Increased timeout for complex generations


@dataclass
class PerformanceConfig:
    """Configuration for performance optimization."""
    max_cache_size: int = 100
    max_parallel_sections: int = 5  # Limit concurrent section processing
    db_connection_timeout: int = 30
    chroma_timeout: int = 45
    max_retries: int = 3
    retry_delay: int = 2


@dataclass
class ValidationConfig:
    """Configuration for validation and compliance checking."""
    enable_government_compliance: bool = True
    min_compliance_score: float = 80.0
    enable_caching: bool = True
    validate_json_structure: bool = True
    max_validation_retries: int = 2


@dataclass
class GovernmentStandards:
    """Government-specific standards and requirements."""
    required_sections: Dict[str, List[str]] = None
    prohibited_phrases: List[str] = None
    numbering_format: str = r'^\d+(\.\d+)*$'
    max_section_depth: int = 4
    
    def __post_init__(self):
        if self.required_sections is None:
            self.required_sections = {
                "technical_approach": ["Technical Approach", "Technical Solution", "Methodology"],
                "past_performance": ["Past Performance", "Demonstrated Experience", "Relevant Experience"],
                "management_approach": ["Management Approach", "Project Management", "Management Plan"],
                "staffing": ["Staffing Plan", "Key Personnel", "Personnel Qualifications"],
                "pricing": ["Pricing", "Cost Proposal", "Price Schedule"]
            }
        
        if self.prohibited_phrases is None:
            self.prohibited_phrases = [
                "marketing", "sales", "promotional", "advertising",
                "best in class", "world-class", "cutting-edge", "revolutionary",
                "unparalleled", "industry-leading", "state-of-the-art"
            ]


@dataclass
class OutlineConfig:
    """Main configuration class for proposal outline generation."""
    llm: LLMConfig = None
    performance: PerformanceConfig = None
    validation: ValidationConfig = None
    government: GovernmentStandards = None
    
    # API endpoints
    embedding_api_url: str = "http://ai.kontratar.com:5000"
    llm_api_url: str = "http://ai.kontratar.com:11434"
    
    # Feature flags
    enable_parallel_processing: bool = True
    enable_enhanced_validation: bool = True
    enable_compliance_checking: bool = True
    enable_response_caching: bool = True
    
    def __post_init__(self):
        if self.llm is None:
            self.llm = LLMConfig()
        if self.performance is None:
            self.performance = PerformanceConfig()
        if self.validation is None:
            self.validation = ValidationConfig()
        if self.government is None:
            self.government = GovernmentStandards()


# Default configuration instance
DEFAULT_CONFIG = OutlineConfig()


def get_config() -> OutlineConfig:
    """Get the default configuration for outline generation."""
    return DEFAULT_CONFIG


def create_custom_config(**kwargs) -> OutlineConfig:
    """Create a custom configuration with overrides."""
    config = OutlineConfig()
    
    # Apply any provided overrides
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    return config


# Environment-specific configurations
DEVELOPMENT_CONFIG = OutlineConfig(
    llm=LLMConfig(temperature=0.2, num_ctx=8000),  # More creative for testing
    performance=PerformanceConfig(max_cache_size=50),
    validation=ValidationConfig(min_compliance_score=70.0)
)

PRODUCTION_CONFIG = OutlineConfig(
    llm=LLMConfig(temperature=0.05, num_ctx=16000),  # More conservative
    performance=PerformanceConfig(max_cache_size=200),
    validation=ValidationConfig(min_compliance_score=85.0)
)

GOVERNMENT_STRICT_CONFIG = OutlineConfig(
    llm=LLMConfig(temperature=0.0, num_ctx=20000),  # Maximum consistency
    performance=PerformanceConfig(max_cache_size=300),
    validation=ValidationConfig(min_compliance_score=90.0, max_validation_retries=5)
)


def get_config_by_environment(env: str = "production") -> OutlineConfig:
    """Get configuration based on environment."""
    configs = {
        "development": DEVELOPMENT_CONFIG,
        "production": PRODUCTION_CONFIG,
        "government": GOVERNMENT_STRICT_CONFIG
    }
    return configs.get(env.lower(), DEFAULT_CONFIG)


# Quality thresholds for different proposal types
QUALITY_THRESHOLDS = {
    "rfp": {
        "min_compliance_score": 85.0,
        "min_sections": 5,
        "max_issues": 3
    },
    "rfi": {
        "min_compliance_score": 75.0,
        "min_sections": 3,
        "max_issues": 5
    },
    "sources_sought": {
        "min_compliance_score": 70.0,
        "min_sections": 2,
        "max_issues": 7
    }
}


def get_quality_threshold(proposal_type: str) -> Dict:
    """Get quality thresholds for a specific proposal type."""
    return QUALITY_THRESHOLDS.get(proposal_type.lower(), QUALITY_THRESHOLDS["rfp"])


# Prompt templates for different scenarios
PROMPT_TEMPLATES = {
    "government_rfp": {
        "system_emphasis": "government compliance and FAR regulations",
        "required_elements": ["technical approach", "past performance", "management plan"],
        "tone": "formal and professional"
    },
    "commercial_rfp": {
        "system_emphasis": "business value and competitive advantage",
        "required_elements": ["solution overview", "implementation plan", "pricing"],
        "tone": "professional and persuasive"
    },
    "rfi_response": {
        "system_emphasis": "information gathering and capability demonstration",
        "required_elements": ["capabilities", "approach", "experience"],
        "tone": "informative and concise"
    }
}


def get_prompt_template(proposal_type: str) -> Dict:
    """Get prompt template for a specific proposal type."""
    return PROMPT_TEMPLATES.get(proposal_type.lower(), PROMPT_TEMPLATES["government_rfp"])
