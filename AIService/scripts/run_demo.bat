@echo off
REM Enhanced Government Proposal Outline Generator - Windows Batch Script
REM This script provides an easy way to run the demo on Windows systems

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🏛️  ENHANCED GOVERNMENT PROPOSAL OUTLINE GENERATOR 🏛️     ║
echo ║                                                              ║
echo ║    Windows Batch Script                                      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python is available

REM Change to the scripts directory
cd /d "%~dp0"

REM Show menu
:menu
echo.
echo 📋 Select an option:
echo.
echo 1. Run Test Mode (No external dependencies)
echo 2. Run Full Demo - Production Environment
echo 3. Run Full Demo - Government Environment  
echo 4. Run Full Demo - Development Environment
echo 5. Check Dependencies
echo 6. Show Usage Examples
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto test_mode
if "%choice%"=="2" goto demo_production
if "%choice%"=="3" goto demo_government
if "%choice%"=="4" goto demo_development
if "%choice%"=="5" goto check_deps
if "%choice%"=="6" goto show_examples
if "%choice%"=="7" goto exit
echo Invalid choice. Please try again.
goto menu

:test_mode
echo.
echo 🧪 Running Test Mode...
echo.
python run_outline_demo.py --mode test
if errorlevel 1 (
    echo.
    echo ❌ Test failed. Check the error messages above.
) else (
    echo.
    echo ✅ Test completed successfully!
    echo 📁 Check the test_results folder for generated files.
)
goto continue

:demo_production
echo.
echo 🚀 Running Full Demo - Production Environment...
echo.
python run_outline_demo.py --mode demo --env production
if errorlevel 1 (
    echo.
    echo ❌ Demo failed. Make sure all services are running.
) else (
    echo.
    echo ✅ Demo completed successfully!
    echo 📁 Check the demo_results folder for generated files.
)
goto continue

:demo_government
echo.
echo 🏛️ Running Full Demo - Government Environment (Strict Compliance)...
echo.
python run_outline_demo.py --mode demo --env government
if errorlevel 1 (
    echo.
    echo ❌ Demo failed. Make sure all services are running.
) else (
    echo.
    echo ✅ Demo completed successfully!
    echo 📁 Check the demo_results folder for generated files.
)
goto continue

:demo_development
echo.
echo 🔧 Running Full Demo - Development Environment...
echo.
python run_outline_demo.py --mode demo --env development
if errorlevel 1 (
    echo.
    echo ❌ Demo failed. Make sure all services are running.
) else (
    echo.
    echo ✅ Demo completed successfully!
    echo 📁 Check the demo_results folder for generated files.
)
goto continue

:check_deps
echo.
echo 🔍 Checking Dependencies...
echo.
python run_outline_demo.py --check-deps
goto continue

:show_examples
echo.
echo 📖 Usage Examples:
echo.
python run_outline_demo.py --examples
goto continue

:continue
echo.
echo Press any key to return to menu...
pause >nul
goto menu

:exit
echo.
echo 👋 Thank you for using the Enhanced Government Proposal Outline Generator!
echo.
pause
exit /b 0
