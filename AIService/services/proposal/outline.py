import re
import json
import asyncio
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from services.chroma.chroma_service import ChromaService

from database import get_customer_db, get_kontratar_db
from langchain_ollama import ChatOllama
from services.proposal.utilities import ProposalUtilities
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from loguru import logger

# Import validation components
try:
    from services.proposal.validation.enhanced_draft_generator import EnhancedDraftGenerator
    from services.proposal.validation.validation_logger import validation_logger
    from services.proposal.validation.government_compliance_validator import GovernmentComplianceValidator
    VALIDATION_AVAILABLE = True
    logger.info("Validation system loaded successfully")
except ImportError as e:
    logger.warning(f"Validation components not available: {e}")
    VALIDATION_AVAILABLE = False
    EnhancedDraftGenerator = None
    validation_logger = None
    GovernmentComplianceValidator = None

def remove_first_markdown_title_regex(text: str) -> str:
    """
    Remove only the first line that starts with ## using regex.

    Args:
        text: Input text containing markdown titles

    Returns:
        Text with only the first ## title removed
    """
    # Remove only the first line starting with ## (with optional whitespace before ##)
    return re.sub(r'^\s*##.*$', '', text, flags=re.MULTILINE, count=1).strip()


def extract_requirements_from_text(text: str, pattern: str) -> List[str]:
    """
    Extract requirements from text using regex patterns.

    Args:
        text: Input text to search
        pattern: Regex pattern to match

    Returns:
        List of matched requirements
    """
    if not text:
        return []

    matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
    return [match.strip() for match in matches if match.strip()]


class ProposalOutlineService:
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.chroma_service = ChromaService(embedding_api_url, None)

        # Optimized LLM configuration for government proposals
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=12000,  # Increased context window for complex government proposals
            temperature=0.1,  # Slight temperature for better creativity while maintaining consistency
            base_url=llm_api_url,
            num_predict=4096,  # Increased output length for detailed sections
            top_p=0.9,  # Nucleus sampling for better quality
            repeat_penalty=1.1  # Reduce repetition
        )

        # Cache for LLM responses to improve performance
        self._response_cache = {}
        self._max_cache_size = 100

        # Database connection pool for better performance
        self._db_connections = {}

        # Initialize government compliance validator
        if VALIDATION_AVAILABLE and GovernmentComplianceValidator:
            self.compliance_validator = GovernmentComplianceValidator()
        else:
            self.compliance_validator = None

    def generate_chroma_query(self, text: str, is_rfp: bool = True):
        if not text:
            return ""

        llm = ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")
        if is_rfp:
            prompt = (
                "Given the following RFP volume information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, evaluation criteria, and any details relevant to the specific volume described below. "
                "This is for a single volume of a multi-volume RFP.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        else:
            prompt = (
                "Given the following RFI information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, government needs, and any details relevant to the RFI topics described below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        response = llm.invoke(prompt)
        return str(response.content)

    def _extract_experience_requirements(self, content_compliance: str) -> str:
        """
        Extract experience requirements from content compliance text.

        Args:
            content_compliance: Text containing compliance requirements

        Returns:
            String describing experience requirements
        """
        if not content_compliance:
            return "3 relevant project experiences as typically required"

        # Look for experience-related patterns
        experience_patterns = [
            r'(\d+)\s+(?:years?|experiences?|projects?|contracts?)\s+(?:of\s+)?(?:relevant\s+)?(?:experience|work)',
            r'(?:minimum|at least)\s+(\d+)\s+(?:experiences?|projects?)',
            r'demonstrate\s+(?:at least\s+)?(\d+)\s+(?:experiences?|projects?)',
            r'past\s+performance.*?(\d+)\s+(?:experiences?|projects?|contracts?)'
        ]

        for pattern in experience_patterns:
            matches = re.findall(pattern, content_compliance, re.IGNORECASE)
            if matches:
                count = matches[0]
                return f"{count} relevant project experiences as required by RFP"

        # Default fallback
        return "3 relevant project experiences (standard government requirement)"

    def _extract_sow_tasks(self, content_compliance: str) -> str:
        """
        Extract Statement of Work tasks from content compliance text.

        Args:
            content_compliance: Text containing SOW tasks

        Returns:
            String listing SOW tasks
        """
        if not content_compliance:
            return "All SOW tasks as specified in the RFP"

        # Look for task-related patterns
        task_patterns = [
            r'task\s+(\d+(?:\.\d+)?)[:\-\s]+([^\n\r]+)',
            r'(\d+(?:\.\d+)?)\.\s*([^\n\r]+?)(?=\d+\.|$)',
            r'sow\s+task[:\s]+([^\n\r]+)',
            r'statement\s+of\s+work[:\s]+([^\n\r]+)'
        ]

        tasks = []
        for pattern in task_patterns:
            matches = re.findall(pattern, content_compliance, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if isinstance(match, tuple) and len(match) == 2:
                    task_num, task_desc = match
                    tasks.append(f"Task {task_num}: {task_desc.strip()}")
                elif isinstance(match, str):
                    tasks.append(match.strip())

        if tasks:
            return "; ".join(tasks[:10])  # Limit to first 10 tasks for prompt efficiency

        return "All SOW tasks as specified in the RFP requirements"

    def _get_cache_key(self, *args) -> str:
        """Generate a cache key from arguments."""
        import hashlib
        key_string = "|".join(str(arg) for arg in args)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Get cached LLM response if available."""
        return self._response_cache.get(cache_key)

    def _cache_response(self, cache_key: str, response: str):
        """Cache LLM response with size limit."""
        if len(self._response_cache) >= self._max_cache_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self._response_cache))
            del self._response_cache[oldest_key]
        self._response_cache[cache_key] = response

    async def _get_db_connection(self, db_type: str = "kontratar"):
        """Get or create database connection with connection pooling."""
        if db_type not in self._db_connections:
            if db_type == "kontratar":
                async for db in get_kontratar_db():
                    self._db_connections[db_type] = db
                    break
            else:  # customer
                async for db in get_customer_db():
                    self._db_connections[db_type] = db
                    break
        return self._db_connections.get(db_type)

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_opportunity called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = None
        if source == "sam":
            logger.info(f"Searching SAM for notice_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                logger.info(f"SAM search result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Searching EBUY for rfq_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                logger.info(f"EBUY search result: {record}")
                break
        elif source == "custom":
            logger.info(f"Searching CUSTOM for opportunity_id={opportunity_id}")
            async for db in get_customer_db():
                record = await self.custom_service.get_main_info_by_opportunity_id(db, opportunity_id)
                logger.info(f"CUSTOM search result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")

        if record is None:
            logger.error(f"Error getting opportunity metadata for id={opportunity_id}, source={source}")
            raise ValueError("Error getting opportunity metadata")

        logger.info(f"Returning opportunity record: {record}")
        return record        

    async def generate_table_of_contents(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        volume_information: str,
        content_compliance: str,
        is_rfp: bool
    ) -> Dict[str, Any]:
        # Restore context retrieval with improved error handling
        context = ""
        chroma_query = self.generate_chroma_query(volume_information, is_rfp)

        if chroma_query:
            async for db in get_kontratar_db():
                max_chunks = 3  # Increased from 1 for better context
                collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                try:
                    logger.info(f"Retrieving context for TOC generation from collection: {collection_name}")
                    relevant_chunks = await asyncio.wait_for(
                        self.chroma_service.get_relevant_chunks(db, collection_name, chroma_query, n_results=max_chunks),
                        timeout=30.0
                    )
                    if relevant_chunks:
                        # Clean up newlines and tabs, preserve important structure
                        toc_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                        context = "\n".join(toc_context)
                        logger.info(f"Retrieved {len(relevant_chunks)} context chunks for TOC generation")
                    else:
                        logger.warning(f"No relevant chunks found for collection {collection_name}")
                except asyncio.TimeoutError:
                    logger.warning(f"ChromaDB timeout for collection {collection_name} - proceeding without context")
                except Exception as e:
                    logger.error(f"Error retrieving chunks: {e} - proceeding without context")
                break

        # Extract experience requirements dynamically from content
        experience_requirements = self._extract_experience_requirements(content_compliance)
        sow_tasks = self._extract_sow_tasks(content_compliance)

        system_prompt = '''
            **Task:**
            You are an expert government proposal writer generating a compliant table of contents for an RFI or RFP Volume.
            You will be given structured information to help you generate this table of contents:
            1. Structure and formatting requirements in <structure-compliance>
            2. Required content and evaluation criteria in <content-compliance>
            3. Relevant opportunity context in <context>

            **Critical Government Compliance Requirements:**
            1. MUST generate ONLY the table of contents for the specific RFI/RFP Volume in <structure-compliance>
            2. MUST include ALL required sections as specified in <content-compliance>
            3. MUST incorporate ALL Statement of Work (SOW) tasks in the Technical Approach section with exact naming
            4. MUST follow government numbering conventions (1.0, 1.1, 1.1.1, etc.)
            5. MUST ensure section descriptions align with evaluation criteria
            6. MUST return valid, parseable JSON following the provided schema exactly

            **Quality Standards:**
            - Use precise government terminology from the RFP
            - Ensure logical section hierarchy and flow
            - Include appropriate subsection granularity for complex requirements
            - Align section descriptions with government evaluation criteria
        '''

        user_prompt = f'''
            Generate a compliant table of contents for this government RFI/RFP:

            <structure-compliance>
                {volume_information}
            </structure-compliance>

            <content-compliance>
                {content_compliance}
            </content-compliance>

            <context>
                {context if context else "No additional context available"}
            </context>

            **Dynamic Requirements Extracted:**
            - Experience Requirements: {experience_requirements}
            - SOW Tasks: {sow_tasks}

            **Instructions:**
            1. Use ONLY information from the sections above
            2. Follow exact naming conventions from <structure-compliance>
            3. Include ALL SOW tasks in Technical Approach section
            4. Add required experience subsections as specified
            5. Ensure government-compliant numbering (1.0, 1.1, 1.1.1)

            **JSON Schema (STRICT COMPLIANCE REQUIRED):**
            {{
                "table_of_contents": [
                    {{
                        "title": "string",
                        "description": "string",
                        "number": "string",
                        "subsections": [
                            {{
                                "number": "string",
                                "title": "string",
                                "description": "string"
                            }}
                        ]
                    }}
                ]
            }}

            **Field Requirements:**
            - "title": Exact section name from RFP requirements
            - "description": Brief content description aligned with evaluation criteria
            - "number": Government format (1.0, 1.1, 1.1.1) starting from 1.0
            - "subsections": Include when section complexity requires breakdown
        '''

        # LLM call with caching and retry logic
        cache_key = self._get_cache_key("toc", system_prompt, user_prompt)
        cached_response = self._get_cached_response(cache_key)

        if cached_response:
            logger.info("Using cached response for table of contents generation")
            return {"content": cached_response}

        content = None
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            content = self.llm.invoke(messages)
            response_content = content.content

            # Cache the response
            self._cache_response(cache_key, response_content)

            logger.info("Generated and cached new table of contents")
            print(response_content)

            # Validate the generated table of contents for government compliance
            if self.compliance_validator:
                try:
                    # Parse the JSON response to validate structure
                    toc_data = json.loads(response_content)
                    if "table_of_contents" in toc_data:
                        compliance_report = self.compliance_validator.validate_outline_compliance(
                            toc_data["table_of_contents"],
                            rfp_requirements=content_compliance,
                            evaluation_criteria=volume_information
                        )

                        logger.info(f"Government compliance validation: {compliance_report.compliance_score:.1f}% compliant")

                        if not compliance_report.is_compliant:
                            logger.warning(f"Compliance issues found: {len(compliance_report.issues)} issues")
                            for issue in compliance_report.issues[:3]:  # Log first 3 issues
                                logger.warning(f"  - {issue.level.value}: {issue.message}")

                        # Add compliance information to response
                        enhanced_response = {
                            "content": response_content,
                            "compliance_report": {
                                "is_compliant": compliance_report.is_compliant,
                                "compliance_score": compliance_report.compliance_score,
                                "issues_count": len(compliance_report.issues),
                                "recommendations": compliance_report.recommendations[:3]
                            }
                        }
                        return enhanced_response

                except json.JSONDecodeError:
                    logger.warning("Could not parse TOC JSON for compliance validation")
                except Exception as e:
                    logger.error(f"Error during compliance validation: {e}")

            return {"content": response_content}
        except Exception as e:
            logger.error(f"Error generating table of contents: {e}")
            raise

    
    async def generate_outline(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a detailed outline for each section and subsection in the table of contents.
        Each outline contains a title, content, and optionally an array of image descriptions.
        The outlines are nested to match the table of contents hierarchy.
        """

        @retry(
            stop=stop_after_attempt(5),  # Retry up to 5 times
            wait=wait_fixed(3),          # Wait 3 seconds between retries
            retry=retry_if_exception_type(Exception),  # Retry on any Exception
            reraise=True                 # Reraise the last exception if all retries fail
        )
        async def outline_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            # Compose a query for this section
            section_title = section.get("title", "")
            section_desc = section.get("description", "")

            logger.info(f"Section title: {section_title}, Section Description: {section_desc}")

            chroma_query = f"""
                Return all the content needed to be seen in proposal section titled '{section_title}'.
            """

            # Initialize context variable
            section_context = ""

            # Fetch relevant context for this section with improved error handling
            try:
                async for db in get_kontratar_db():
                    collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                    try:
                        relevant_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, collection_name, chroma_query, n_results=3),
                            timeout=30.0
                        )
                        if relevant_chunks:
                            context_chunks = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                            section_context = "\n".join(context_chunks)
                            logger.info(f"Retrieved {len(relevant_chunks)} context chunks for section: {section_title}")
                        else:
                            logger.warning(f"No context chunks found for section: {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for section {section_title} - proceeding without context")
                    except Exception as e:
                        logger.error(f"Error retrieving context for section {section_title}: {e}")
                    break
            except Exception as e:
                logger.error(f"Database connection error for section {section_title}: {e}")
                section_context = ""

            system_prompt = '''
                **Task:**
                For the given proposal section, generate a detailed outline.
                The outline should include:
                - A title (from the section/subsection)
                - Content: a detailed guide on what to include, and what NOT to include if relevant, for that section/subsection.
                - Page limit: the max number of pages allowed for this section
                - References: References from <context> used to generate the section of this outline. Whatver reference you include, 
                show the full text of it.
                - Purpose: The purpose of this section eg. To Persuade, To Identify, to Justify
                - Opportunity vector DB query: A detailed vector database query that will get information about the government 
                opportunity relevant to this section.
                - Client Vector DB Query: A detailed vector database query to get information about the client relevant to respond to this section.
                - Custom Prompt: A very specific, customized and in-depth prompt to successfully generate 
                content for this section. It should also contain step by step instructins on what to do. Using your knowledge, if there 
                are any tables or diagrams that MUST be included, please say so as well.
                - Optionally, an array of image_descriptions if an image or diagram is necessary for that section/subsection.

                **Important:**
                1. Use only information found in <context> and the section/subsection title/description to build the outline.
                2. Custom Prompt must include the usage of naming conventions found in RFP/RFI.
                3. ANY table that must be created MUST be included in the "image_descriptions" array.
                4. The response for each section/subsection MUST be valid JSON and follow the schema strictly.
                5. Do not invent content; base your outline on best practices and the provided context.
                6. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
                7. You will be given a JSON schema to comply to, ensure to follow it strictly.

                **Special Sections Instructions (Regardless of Volume):**
                1. Staffing Plan sections MUST always have a table (Columns: Role, Responsibilities, Qualifications)
                2. Technical Approach should always include the SOW tasks and how they would be approached
                3. Pricing sections MAY or MAY not need a table
                
            '''

            user_prompt = f'''
                <section-title>
                    {section_title}
                </section-title>

                <section-description>
                    {section_desc}
                </section-description>

                <context>
                    {section_context if section_context else "No specific context available for this section"}
                </context>

                Use the following JSON schema for your response:
                {{
                    "title": "string",
                    "content": "string",
                    "page_limit": number,
                    "purpose": "string",
                    "rfp_vector_db_query": "string",
                    "client_vector_db_query": "string",
                    "custom_prompt": "string",
                    "references": "string",
                    "image_descriptions": ["string"]  // Only include this field if an image is necessary
                }}
            '''

            # LLM call with retry logic
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)
            content = str(result.content)
            
            # Parse the LLM output (assume it's valid JSON)
            outline = ProposalUtilities.extract_json_from_brackets(content)

            if outline is None:
                return {}
            
            print(f"Raw content: {content}, Processed Content: {outline}")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                outline["subsections"] = []
                for subsection in subsections:
                    sub_outline = await outline_for_section(subsection)
                    outline["subsections"].append(sub_outline)

            return outline

        # Build the nested outline structure with parallel processing
        logger.info(f"Processing {len(table_of_contents)} sections in parallel for outline generation")

        # Process sections in parallel for better performance
        outline_tasks = [outline_for_section(section) for section in table_of_contents]
        outlines = await asyncio.gather(*outline_tasks, return_exceptions=True)

        # Handle any exceptions and filter out failed sections
        successful_outlines = []
        for i, outline in enumerate(outlines):
            if isinstance(outline, Exception):
                logger.error(f"Failed to generate outline for section {i}: {outline}")
                # Create a minimal outline for failed sections
                section = table_of_contents[i]
                failed_outline = {
                    "title": section.get("title", f"Section {i+1}"),
                    "content": "Failed to generate outline - please review manually",
                    "page_limit": 2,
                    "purpose": "To be determined",
                    "rfp_vector_db_query": "",
                    "client_vector_db_query": "",
                    "custom_prompt": "",
                    "references": "",
                    "image_descriptions": []
                }
                successful_outlines.append(failed_outline)
            else:
                successful_outlines.append(outline)

        logger.info(f"Successfully generated {len(successful_outlines)} outlines")
        return {"outlines": successful_outlines}


    ## This is to generate a draft
    async def generate_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft for each section and subsection in the table of contents.
        The drafts are nested to match the table of contents hierarchy.
        """

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        @retry(
            stop=stop_after_attempt(5),  # Retry up to 3 times
            wait=wait_fixed(3),          # Wait 1 second between retries
            retry=retry_if_exception_type(Exception),  # Retry on any Exception
            reraise=True                 # Reraise the last exception if all retries fail
        )
        async def draft_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            # Compose a query for this section
            section_title = section.get("title", "")
            section_desc = section.get("description", "")
            section_number = section.get("number", "")

            logger.info(f"Section title: {section_title}, Section Description: {section_desc}")
            
            # Initialize context variables
            rfp_context = ""
            client_context_str = ""

            # Fetch relevant context for this section with improved error handling
            chroma_query = f"""
                Return all the content needed to be seen in proposal section titled '{section_title}'.
            """

            client_query = f"""
                Return information relevant to a proposal section titled '{section_title}'
            """

            try:
                async for db in get_kontratar_db():
                    # Get RFP context
                    opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                    try:
                        opportunity_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, opportunity_collection, chroma_query, n_results=3),
                            timeout=30.0
                        )
                        if opportunity_chunks:
                            rfp_context_chunks = [chunk.replace("\n", " ").replace("\t", " ") for chunk in opportunity_chunks]
                            rfp_context = "\n".join(rfp_context_chunks)
                            logger.info(f"Retrieved RFP context for section: {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for RFP context in section: {section_title}")
                    except Exception as e:
                        logger.error(f"Error retrieving RFP context for section {section_title}: {e}")

                    # Get client context
                    client_collection = f"{tenant_id}_{client_short_name}"
                    try:
                        relevant_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, client_collection, client_query, n_results=3),
                            timeout=30.0
                        )
                        if relevant_chunks:
                            client_context_chunks = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                            client_context_str = "\n".join(client_context_chunks)
                            logger.info(f"Retrieved client context for section: {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for client context in section: {section_title}")
                    except Exception as e:
                        logger.error(f"Error retrieving client context for section {section_title}: {e}")

                    break
            except Exception as e:
                logger.error(f"Database connection error for section {section_title}: {e}")
                rfp_context = ""
                client_context_str = ""
            

            system_prompt = '''
                **Task:**
                Generate professional government proposal content with 100% compliance.

                **CRITICAL REQUIREMENTS:**
                1. NO placeholders, brackets, TBD, TODO, or incomplete information
                2. NO contact info in technical sections (except cover letters)
                3. NO repetition of RFP requirements or administrative details
                4. NO generic marketing language - be specific and evidence-based
                5. FOCUS ONLY on demonstrating capability for the requested work
                6. Use concrete methodologies and measurable outcomes
                7. Write for government evaluators assessing technical capability
                8. COMPLY with page limits

                **CONTENT STANDARDS:**
                - Demonstrate HOW you will perform the work with specific processes
                - Show understanding of government requirements
                - Provide specific methodologies, tools, and success criteria
                - Focus on capability demonstration, not company marketing
                - Use professional, direct language
                - Structure content for easy evaluation (headers, bullets, tables)
                - Include specific metrics, timelines, and deliverables
                - Keep paragraphs concise (3-5 sentences maximum)

                **FORMATTING STANDARDS:**
                - Use clear section headers and subheaders
                - Include bullet points for key information
                - Generate relevant tables in markdown when they support capability demonstration
                - Tables must be complete with specific data - NO placeholders
                - Use active voice and strong action verbs
                - Ensure content is scannable and easy to evaluate
            '''

            # Check if this is a cover letter section
            is_cover_letter = any(keyword in section_title.lower() for keyword in ['cover', 'transmittal', 'letter'])

            if is_cover_letter:
                # Get real opportunity data for cover letter
                opportunity_title = getattr(record, 'title', 'Government Consulting Services')
                opportunity_description = getattr(record, 'description', 'Professional services opportunity')

                # Generate current date
                from datetime import datetime
                current_date = datetime.now().strftime("%B %d, %Y")

                user_prompt = f'''
                    Write a formal government proposal cover letter using the company information provided.

                    **CRITICAL REQUIREMENTS:**
                    - NO placeholders, brackets, or [Replace with...] text
                    - NO markdown code blocks or ``` formatting
                    - Extract company information from the tenant metadata provided
                    - Use proper business letter format
                    - NO fabricated or hallucinated information

                    **OPPORTUNITY INFORMATION:**
                    - Date: {current_date}
                    - Opportunity: {opportunity_title}
                    - Description: {opportunity_description}
                    - Opportunity ID: {opportunity_id}

                    **COMPANY INFORMATION (extract from this):**
                    {tenant_metadata}

                    **FORMAT TO FOLLOW:**
                    COVER LETTER
                    [Current Date]
                    [Government Agency]
                    [Agency Address]

                    Reference: [Opportunity Title]
                    Solicitation: [Opportunity ID]

                    Dear Contracting Officer,

                    [Company name] is pleased to submit this proposal in response to the above-referenced solicitation. [Brief company capability statement related to the opportunity].

                    [One paragraph about relevant experience and qualifications]

                    We look forward to the opportunity to support [agency] in this important initiative.

                    Sincerely,

                    [Contact Name from company info]
                    [Company Name]
                    [Email if available]

                    Extract the company name, contact person, and email from the tenant metadata and use them in the letter.
                '''
            else:
                user_prompt = f'''
                    Write a {section_title} section for a government proposal.

                    **REQUIREMENTS:**
                    - Return ONLY the section content - NO titles or explanations
                    - NO placeholders, brackets, TBD, TODO, or incomplete information
                    - NO contact info, generic marketing, or RFP repetition
                    - Demonstrate HOW you will perform the work with specific processes
                    - Use concrete methodologies and measurable outcomes
                    - Comply with page limits (typically 2-5 pages per section)
                    - Use clear structure with headers and bullet points where appropriate

                    **RFP CONTEXT:**
                    {rfp_context[:1500] if rfp_context else "No RFP context provided"}

                    **COMPANY INFORMATION:**
                    {tenant_metadata[:800] if tenant_metadata else ""}
                    {client_context_str[:800] if client_context_str else ""}

                    Generate professional content demonstrating specific technical capability.
                '''

            # LLM call with retry logic
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            result = self.llm.invoke(messages)
            content = str(result.content)
            
            text = remove_first_markdown_title_regex(content)

            text = text.strip()
            if text.startswith('```'):
                first_newline = text.find('\n')
                if first_newline != -1:
                    text = text[first_newline + 1:]

            if text.endswith('```'):
                text = text[:-3]
            text = text.replace('```', '')
            text = text.strip()

            draft = { 
                "title": section_number + " " + section_title, 
                "content": text, 
                "number": section_number 
            }
            
            print(f"Raw content: {text}")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                draft["subsections"] = []
                for subsection in subsections:
                    sub_outline = await draft_for_section(subsection)
                    draft["subsections"].append(sub_outline)

            return draft

        # Build the nested draft structure with parallel processing
        logger.info(f"Processing {len(table_of_contents)} sections in parallel for draft generation")

        # Process sections in parallel for better performance
        draft_tasks = [draft_for_section(section) for section in table_of_contents]
        drafts = await asyncio.gather(*draft_tasks, return_exceptions=True)

        # Handle any exceptions and filter out failed sections
        successful_drafts = []
        for i, draft in enumerate(drafts):
            if isinstance(draft, Exception):
                logger.error(f"Failed to generate draft for section {i}: {draft}")
                # Create a minimal draft for failed sections
                section = table_of_contents[i]
                failed_draft = {
                    "title": section.get("title", f"Section {i+1}"),
                    "content": "Failed to generate draft content - please review and regenerate manually",
                    "number": section.get("number", f"{i+1}.0")
                }
                successful_drafts.append(failed_draft)
            else:
                successful_drafts.append(draft)

        logger.info(f"Successfully generated {len(successful_drafts)} draft sections")
        return {"draft": successful_drafts}

    ## Enhanced draft generation with validation
    async def generate_validated_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft with integrated validation
        """

        if not VALIDATION_AVAILABLE:
            logger.warning("Validation system not available, falling back to standard draft generation")
            return await self.generate_draft(
                opportunity_id, tenant_id, source, client_short_name, tenant_metadata, table_of_contents
            )

        logger.info(f"Starting enhanced draft generation with AUTO-FIXING validation for opportunity {opportunity_id}")

        # Initialize validation session
        session_id = validation_logger.start_validation_session(
            opportunity_id, tenant_id, len(table_of_contents)
        )

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        # Use the new enhanced generator with auto-fixing
        from services.proposal.enhanced_draft_generator import EnhancedDraftGenerator
        enhanced_generator = EnhancedDraftGenerator()

        @retry(
            stop=stop_after_attempt(3),
            wait=wait_fixed(2),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def validated_draft_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            section_title = section.get("title", "")
            section_desc = section.get("description", "")
            section_number = section.get("number", "")

            logger.info(f"Generating validated draft for section: {section_number} - {section_title}")

            # Fetch relevant context for this section
            chroma_query = f"Return all content needed for proposal section titled '{section_title}'"
            client_query = f"Return information relevant to proposal section titled '{section_title}'"

            # Initialize fallback contexts
            rfp_context = f"Section: {section_title}\nDescription: {section_desc}"
            company_context = "Professional services company with expertise in government contracting."

            try:
                async for db in get_kontratar_db():
                    opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    # Try to get opportunity chunks with timeout
                    try:
                        logger.info(f"Fetching RFP context for {section_title} from {opportunity_collection}")
                        opportunity_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, opportunity_collection, chroma_query, n_results=5),
                            timeout=45.0
                        )
                        rfp_context = "\n".join([chunk.replace("\n", " ").replace("\t", " ") for chunk in opportunity_chunks])
                        logger.info(f"Retrieved RFP context ({len(rfp_context)} chars) for {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for opportunity collection {opportunity_collection} - using fallback")
                    except Exception as e:
                        logger.error(f"Error retrieving opportunity chunks: {e} - using fallback")

                    # Try to get client chunks with timeout
                    client_collection = f"{tenant_id}_{client_short_name}"
                    try:
                        logger.info(f"Fetching company context for {section_title} from {client_collection}")
                        relevant_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, client_collection, client_query, n_results=3),
                            timeout=45.0
                        )
                        company_context = "\n".join([chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks])
                        logger.info(f"Retrieved company context ({len(company_context)} chars) for {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for client collection {client_collection} - using fallback")
                    except Exception as e:
                        logger.error(f"Error retrieving client chunks: {e} - using fallback")

                    break

            except Exception as e:
                logger.error(f"Database connection error: {e} - using minimal fallback context")

            # Add tenant metadata to company context
            full_company_context = f"{tenant_metadata}\n\n{company_context}"

            # Generate validated draft using enhanced generator
            draft_result = await enhanced_generator.generate_validated_draft(
                section_title=section_title,
                section_desc=section_desc,
                section_number=section_number,
                rfp_context=rfp_context,
                company_context=full_company_context,
                max_retries=3
            )

            logger.info(f"Generated validated draft for {section_number} - Validation Score: {draft_result['validation_score']:.1f}%")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                draft_result["subsections"] = []
                for subsection in subsections:
                    sub_draft = await validated_draft_for_section(subsection)
                    draft_result["subsections"].append(sub_draft)

            return draft_result

        # Generate AUTO-FIXED validated proposal using enhanced generator
        try:
            # Get opportunity context
            opportunity_context = str(record) if record else ""

            # Generate complete validated proposal with auto-fixing
            proposal_result = await enhanced_generator.generate_validated_proposal(
                table_of_contents=table_of_contents,
                opportunity_context=opportunity_context,
                company_context="",
                tenant_metadata=tenant_metadata
            )

            # Convert to expected format
            drafts = []
            for section_result in proposal_result["sections"]:
                draft_section = {
                    "title": section_result["title"],
                    "description": section_result["description"],
                    "content": section_result["content"],
                    "validation_passed": section_result["validation_passed"],
                    "issues_found": section_result["issues_found"],
                    "fixes_applied": section_result["fixes_applied"]
                }
                drafts.append(draft_section)

            # Create enhanced validation summary
            validation_summary = {
                "overall_valid": proposal_result["validation_summary"]["passed"],
                "validation_score": proposal_result["quality_score"],
                "valid_sections": proposal_result["perfect_sections"],
                "total_sections": proposal_result["total_sections"],
                "total_issues": proposal_result["total_issues_found"],
                "total_fixes_applied": proposal_result["total_fixes_applied"],
                "quality_grade": "A" if proposal_result["quality_score"] >= 90 else "B" if proposal_result["quality_score"] >= 80 else "C"
            }

            validation_logger.complete_validation_session(validation_summary)

            logger.info(f"ENHANCED: AUTO-FIX VALIDATION SUMMARY:")
            logger.info(f"   Overall Valid: {validation_summary['overall_valid']}")
            logger.info(f"   Quality Score: {validation_summary['validation_score']:.1f}%")
            logger.info(f"   Quality Grade: {validation_summary['quality_grade']}")
            logger.info(f"   Perfect Sections: {validation_summary['valid_sections']}/{validation_summary['total_sections']}")
            logger.info(f"   Total Issues Found: {validation_summary['total_issues']}")
            logger.info(f"   Total Fixes Applied: {validation_summary['total_fixes_applied']}")

            return {
                "draft": drafts,
                "validation_summary": validation_summary,
                "session_id": session_id,
                "auto_fixing_enabled": True,
                "quality_score": proposal_result["quality_score"]
            }

        except Exception as e:
            validation_logger.log_critical_error(f"Failed to generate validated draft: {str(e)}", e)
            raise

    

