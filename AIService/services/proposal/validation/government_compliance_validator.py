"""
Government Compliance Validator for Proposal Outline Generation

This module provides specialized validation for government proposal requirements,
ensuring compliance with federal contracting standards and RFP specifications.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ComplianceLevel(Enum):
    """Compliance severity levels for government proposals."""
    CRITICAL = "CRITICAL"  # Will cause proposal rejection
    HIGH = "HIGH"         # Major compliance issue
    MEDIUM = "MEDIUM"     # Minor compliance issue
    LOW = "LOW"          # Best practice recommendation


@dataclass
class ComplianceIssue:
    """Represents a compliance issue found in the proposal."""
    level: ComplianceLevel
    category: str
    message: str
    section: str
    suggested_fix: str
    regulation_reference: Optional[str] = None


@dataclass
class ComplianceReport:
    """Comprehensive compliance validation report."""
    is_compliant: bool
    compliance_score: float  # 0-100
    issues: List[ComplianceIssue]
    passed_checks: List[str]
    recommendations: List[str]


class GovernmentComplianceValidator:
    """
    Validates proposal outlines against government compliance requirements.
    
    This validator checks for:
    - FAR (Federal Acquisition Regulation) compliance
    - Section completeness and structure
    - Government-specific formatting requirements
    - Required content elements
    - Evaluation criteria alignment
    """
    
    def __init__(self):
        self.required_sections = {
            "technical_approach": ["Technical Approach", "Technical Solution", "Methodology"],
            "past_performance": ["Past Performance", "Demonstrated Experience", "Relevant Experience"],
            "management_approach": ["Management Approach", "Project Management", "Management Plan"],
            "staffing": ["Staffing Plan", "Key Personnel", "Personnel Qualifications"],
            "pricing": ["Pricing", "Cost Proposal", "Price Schedule"]
        }
        
        self.government_keywords = [
            "FAR", "DFARS", "GSA", "SEWP", "CIO-SP3", "OASIS", "8(a)", "HUBZone",
            "SDVOSB", "WOSB", "SBA", "NAICS", "PSC", "PWS", "SOW", "CLIN", "CPFF",
            "FFP", "T&M", "IDIQ", "BPA", "GSA Schedule"
        ]

    def validate_outline_compliance(
        self, 
        table_of_contents: List[Dict[str, Any]], 
        rfp_requirements: str = "",
        evaluation_criteria: str = ""
    ) -> ComplianceReport:
        """
        Validate proposal outline against government compliance requirements.
        
        Args:
            table_of_contents: The generated table of contents
            rfp_requirements: RFP requirements text
            evaluation_criteria: Evaluation criteria from RFP
            
        Returns:
            ComplianceReport with detailed validation results
        """
        logger.info("Starting government compliance validation")
        
        issues = []
        passed_checks = []
        
        # Check required sections
        section_issues, section_passes = self._validate_required_sections(table_of_contents)
        issues.extend(section_issues)
        passed_checks.extend(section_passes)
        
        # Check section structure and numbering
        structure_issues, structure_passes = self._validate_section_structure(table_of_contents)
        issues.extend(structure_issues)
        passed_checks.extend(structure_passes)
        
        # Check content descriptions for government compliance
        content_issues, content_passes = self._validate_content_descriptions(table_of_contents)
        issues.extend(content_issues)
        passed_checks.extend(content_passes)
        
        # Check RFP alignment if provided
        if rfp_requirements:
            rfp_issues, rfp_passes = self._validate_rfp_alignment(table_of_contents, rfp_requirements)
            issues.extend(rfp_issues)
            passed_checks.extend(rfp_passes)
        
        # Check evaluation criteria alignment
        if evaluation_criteria:
            eval_issues, eval_passes = self._validate_evaluation_alignment(table_of_contents, evaluation_criteria)
            issues.extend(eval_issues)
            passed_checks.extend(eval_passes)
        
        # Calculate compliance score
        total_checks = len(issues) + len(passed_checks)
        compliance_score = (len(passed_checks) / total_checks * 100) if total_checks > 0 else 0
        
        # Determine overall compliance
        critical_issues = [i for i in issues if i.level == ComplianceLevel.CRITICAL]
        is_compliant = len(critical_issues) == 0 and compliance_score >= 80
        
        # Generate recommendations
        recommendations = self._generate_recommendations(issues, compliance_score)
        
        logger.info(f"Compliance validation complete: {compliance_score:.1f}% compliant, {len(issues)} issues found")
        
        return ComplianceReport(
            is_compliant=is_compliant,
            compliance_score=compliance_score,
            issues=issues,
            passed_checks=passed_checks,
            recommendations=recommendations
        )

    def _validate_required_sections(self, toc: List[Dict[str, Any]]) -> Tuple[List[ComplianceIssue], List[str]]:
        """Validate that all required government proposal sections are present."""
        issues = []
        passed = []
        
        # Extract all section titles from TOC
        all_titles = []
        for section in toc:
            all_titles.append(section.get("title", "").lower())
            for subsection in section.get("subsections", []):
                all_titles.append(subsection.get("title", "").lower())
        
        # Check each required section category
        for category, required_titles in self.required_sections.items():
            found = False
            for required_title in required_titles:
                if any(required_title.lower() in title for title in all_titles):
                    found = True
                    passed.append(f"Required section found: {required_title}")
                    break
            
            if not found:
                issues.append(ComplianceIssue(
                    level=ComplianceLevel.CRITICAL,
                    category="Missing Required Section",
                    message=f"Missing required section: {category}. Must include one of: {', '.join(required_titles)}",
                    section="Table of Contents",
                    suggested_fix=f"Add a section for {required_titles[0]} to address government evaluation criteria",
                    regulation_reference="FAR 15.304 - Evaluation factors"
                ))
        
        return issues, passed

    def _validate_section_structure(self, toc: List[Dict[str, Any]]) -> Tuple[List[ComplianceIssue], List[str]]:
        """Validate section numbering and structure follows government standards."""
        issues = []
        passed = []
        
        # Check numbering format (should be 1.0, 1.1, 1.1.1, etc.)
        numbering_pattern = re.compile(r'^\d+(\.\d+)*$')
        
        for i, section in enumerate(toc):
            section_number = section.get("number", "")
            section_title = section.get("title", f"Section {i+1}")
            
            if not section_number:
                issues.append(ComplianceIssue(
                    level=ComplianceLevel.HIGH,
                    category="Missing Section Number",
                    message=f"Section '{section_title}' is missing a number",
                    section=section_title,
                    suggested_fix="Add proper government-style numbering (e.g., 1.0, 2.0, etc.)"
                ))
            elif not numbering_pattern.match(section_number):
                issues.append(ComplianceIssue(
                    level=ComplianceLevel.MEDIUM,
                    category="Invalid Numbering Format",
                    message=f"Section '{section_title}' has invalid numbering format: {section_number}",
                    section=section_title,
                    suggested_fix="Use government standard numbering format (1.0, 1.1, 1.1.1)"
                ))
            else:
                passed.append(f"Valid numbering format: {section_number}")
            
            # Check subsection numbering
            for j, subsection in enumerate(section.get("subsections", [])):
                sub_number = subsection.get("number", "")
                sub_title = subsection.get("title", f"Subsection {j+1}")
                
                if sub_number and not numbering_pattern.match(sub_number):
                    issues.append(ComplianceIssue(
                        level=ComplianceLevel.MEDIUM,
                        category="Invalid Subsection Numbering",
                        message=f"Subsection '{sub_title}' has invalid numbering: {sub_number}",
                        section=section_title,
                        suggested_fix="Use hierarchical numbering (e.g., 1.1, 1.2, 1.1.1)"
                    ))
        
        return issues, passed

    def _validate_content_descriptions(self, toc: List[Dict[str, Any]]) -> Tuple[List[ComplianceIssue], List[str]]:
        """Validate that content descriptions are appropriate for government proposals."""
        issues = []
        passed = []
        
        prohibited_phrases = [
            "marketing", "sales", "promotional", "advertising",
            "best in class", "world-class", "cutting-edge", "revolutionary"
        ]
        
        required_elements = {
            "technical": ["methodology", "approach", "process", "solution"],
            "management": ["plan", "process", "oversight", "control"],
            "staffing": ["qualifications", "experience", "roles", "responsibilities"]
        }
        
        for section in toc:
            section_title = section.get("title", "")
            description = section.get("description", "").lower()
            
            # Check for prohibited marketing language
            for phrase in prohibited_phrases:
                if phrase in description:
                    issues.append(ComplianceIssue(
                        level=ComplianceLevel.MEDIUM,
                        category="Inappropriate Language",
                        message=f"Section '{section_title}' contains marketing language: '{phrase}'",
                        section=section_title,
                        suggested_fix="Replace with factual, capability-focused language"
                    ))
            
            # Check for required elements based on section type
            section_type = self._categorize_section(section_title)
            if section_type in required_elements:
                found_elements = [elem for elem in required_elements[section_type] if elem in description]
                if not found_elements:
                    issues.append(ComplianceIssue(
                        level=ComplianceLevel.HIGH,
                        category="Missing Required Content Elements",
                        message=f"Section '{section_title}' description lacks required elements for {section_type} sections",
                        section=section_title,
                        suggested_fix=f"Include elements like: {', '.join(required_elements[section_type])}"
                    ))
                else:
                    passed.append(f"Section '{section_title}' includes appropriate {section_type} elements")
        
        return issues, passed

    def _validate_rfp_alignment(self, toc: List[Dict[str, Any]], rfp_requirements: str) -> Tuple[List[ComplianceIssue], List[str]]:
        """Validate alignment with specific RFP requirements."""
        issues = []
        passed = []
        
        # Extract SOW tasks from RFP requirements
        sow_tasks = self._extract_sow_tasks(rfp_requirements)
        
        if sow_tasks:
            # Check if technical approach includes all SOW tasks
            tech_sections = [s for s in toc if "technical" in s.get("title", "").lower()]
            
            if not tech_sections:
                issues.append(ComplianceIssue(
                    level=ComplianceLevel.CRITICAL,
                    category="Missing SOW Coverage",
                    message="No technical approach section found to address SOW tasks",
                    section="Table of Contents",
                    suggested_fix="Add Technical Approach section with subsections for each SOW task"
                ))
            else:
                passed.append("Technical approach section found for SOW coverage")
        
        return issues, passed

    def _validate_evaluation_alignment(self, toc: List[Dict[str, Any]], evaluation_criteria: str) -> Tuple[List[ComplianceIssue], List[str]]:
        """Validate alignment with RFP evaluation criteria."""
        issues = []
        passed = []
        
        # Extract evaluation factors
        eval_factors = self._extract_evaluation_factors(evaluation_criteria)
        
        for factor in eval_factors:
            # Check if TOC has sections addressing each evaluation factor
            factor_addressed = any(
                factor.lower() in section.get("title", "").lower() or 
                factor.lower() in section.get("description", "").lower()
                for section in toc
            )
            
            if factor_addressed:
                passed.append(f"Evaluation factor addressed: {factor}")
            else:
                issues.append(ComplianceIssue(
                    level=ComplianceLevel.HIGH,
                    category="Missing Evaluation Factor Coverage",
                    message=f"No section clearly addresses evaluation factor: {factor}",
                    section="Table of Contents",
                    suggested_fix=f"Add section or subsection specifically addressing {factor}"
                ))
        
        return issues, passed

    def _categorize_section(self, title: str) -> str:
        """Categorize section based on title."""
        title_lower = title.lower()
        if any(word in title_lower for word in ["technical", "approach", "solution", "methodology"]):
            return "technical"
        elif any(word in title_lower for word in ["management", "plan", "oversight"]):
            return "management"
        elif any(word in title_lower for word in ["staff", "personnel", "team", "qualifications"]):
            return "staffing"
        return "general"

    def _extract_sow_tasks(self, rfp_text: str) -> List[str]:
        """Extract SOW tasks from RFP text."""
        # Simple pattern matching for SOW tasks
        task_patterns = [
            r'task\s+(\d+(?:\.\d+)?)[:\-\s]+([^\n\r]+)',
            r'(\d+(?:\.\d+)?)\.\s*([^\n\r]+?)(?=\d+\.|$)'
        ]
        
        tasks = []
        for pattern in task_patterns:
            matches = re.findall(pattern, rfp_text, re.IGNORECASE | re.MULTILINE)
            tasks.extend([f"Task {match[0]}: {match[1].strip()}" for match in matches])
        
        return tasks[:10]  # Limit to first 10 tasks

    def _extract_evaluation_factors(self, eval_text: str) -> List[str]:
        """Extract evaluation factors from evaluation criteria text."""
        # Common government evaluation factors
        common_factors = [
            "Technical Approach", "Past Performance", "Management Approach",
            "Staffing Plan", "Price", "Cost", "Small Business Participation"
        ]
        
        found_factors = []
        eval_lower = eval_text.lower()
        
        for factor in common_factors:
            if factor.lower() in eval_lower:
                found_factors.append(factor)
        
        return found_factors

    def _generate_recommendations(self, issues: List[ComplianceIssue], score: float) -> List[str]:
        """Generate actionable recommendations based on compliance issues."""
        recommendations = []
        
        if score < 60:
            recommendations.append("URGENT: Major compliance issues detected. Recommend complete outline review.")
        elif score < 80:
            recommendations.append("Moderate compliance issues found. Address high-priority items before submission.")
        else:
            recommendations.append("Good compliance level. Address remaining minor issues for optimal quality.")
        
        # Category-specific recommendations
        categories = {}
        for issue in issues:
            if issue.category not in categories:
                categories[issue.category] = 0
            categories[issue.category] += 1
        
        for category, count in categories.items():
            if count > 2:
                recommendations.append(f"Multiple {category} issues detected ({count}). Consider systematic review.")
        
        return recommendations
