2025-08-03 13:26:35 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 13:26:35 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 13:26:35 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 13:26:35 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 13:26:35 | INFO     | VALIDATION | Generating outline...
2025-08-03 13:26:35 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:26:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:27:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:27:05 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:27:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:27:51 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:27:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:28:17 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:28:17 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:28:17 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:29:09 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:29:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:29:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:29:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:29:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:30:37 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 13:30:37 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 13:30:37 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 13:30:37 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 13:30:38 | INFO     | VALIDATION | Generating outline...
2025-08-03 13:30:38 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:30:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:31:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:31:04 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:31:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:32:05 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:32:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:32:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:32:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:32:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:33:39 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:33:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:33:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:33:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:33:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:34:54 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:34:54 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:34:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:34:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:34:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:35:43 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:35:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:35:44 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:35:44 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:35:44 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:36:55 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:36:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:36:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:36:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:36:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:37:38 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:37:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:37:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:37:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:37:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:38:38 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:38:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:38:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:38:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:38:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:39:36 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:39:36 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:39:38 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:39:38 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:39:38 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:40:43 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:40:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:40:45 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:40:45 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:40:45 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:41:44 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:41:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:41:46 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:41:46 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:41:46 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:42:39 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:42:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:42:41 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:42:41 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:42:41 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:44:04 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:44:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:44:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:44:05 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:44:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:45:07 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:45:07 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:45:09 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:45:09 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:45:09 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:46:13 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:46:13 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:46:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:46:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:46:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:47:22 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:47:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:47:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:47:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:47:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:48:23 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:48:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:48:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:48:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:48:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:49:28 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:49:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:49:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:49:30 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:49:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:50:27 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:50:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:50:29 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:50:29 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:50:29 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:51:34 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:51:34 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:51:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:51:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:51:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:52:37 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:52:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:52:39 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:52:39 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:52:39 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:53:39 | INFO     | VALIDATION | Outline generated successfully and saved to generated_outline_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2.json
2025-08-03 13:53:39 | INFO     | VALIDATION | Generated outline contains 5 main sections
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 1: Tab A - Proposal Cover/Transmittal Letter (0 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 2: Tab B - Factor 1 - Staffing & Key Personnel Qualifications (4 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 3: Tab C - Factor 2 - Management Approach (3 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 4: Tab D - Factor 3 - Technical Approach (6 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 5: Tab E - Factor 4 - Demonstrated Corporate Experience (3 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION | Outline generation completed successfully!
2025-08-03 14:49:34 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 14:49:34 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 14:49:34 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 14:49:34 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:49:34 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:49:34 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:11 | WARNING  | VALIDATION | Timeout getting requirements context for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 14:50:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:50:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:50:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 14:50:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 14:50:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:50:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 14:50:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 14:50:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 14:51:53 | ERROR    | VALIDATION | Failed to parse outline for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 14:51:53 | ERROR    | VALIDATION | Error generating enhanced outline for Tab A - Proposal Cover/Transmittal Letter: Failed to generate valid outline for section: Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:08:27 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:08:27 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 15:08:27 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 15:08:27 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 15:08:28 | INFO     | VALIDATION | Generating outline...
2025-08-03 15:08:28 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 15:08:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:08:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:08:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:08:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:08:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:08:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:09:42 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 15:09:42 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:09:42 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:09:42 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:09:55 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:09:55 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:09:55 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:11:01 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 15:11:01 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:01 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:11:01 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:11:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:11:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:11:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:11:38 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:11:39 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 15:11:39 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:11:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:11:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:11:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:08 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:08 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:08 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:09 | WARNING  | VALIDATION | Timeout getting requirements context for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:12:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:12:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:09 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:09 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:09 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:12:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:12 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:12 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:12 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:15 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:12:15 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:12:15 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:12:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:12:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:12:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:12 | INFO     | VALIDATION | Generating enhanced outline for: 2.0 - Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-03 15:13:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:27 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:31 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:31 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:31 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:31 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:13:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:13:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:13:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:13:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:13:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:50 | INFO     | VALIDATION | Generating enhanced outline for: 2.1 - Recruitment, Hiring, and Retention Approach
2025-08-03 15:14:50 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:50 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:14:50 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:51 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:14:51 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:14:51 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:14:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:14:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:14:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:14:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:14:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:15:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:15:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:15:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:03 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:15:03 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:15:03 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:15:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:15:04 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:15:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:08 | INFO     | VALIDATION | Generating enhanced outline for: 2.2 - Certifications and Training Processes
2025-08-03 15:16:08 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:08 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:08 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:10 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:10 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:10 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:19 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:19 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:19 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:16:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:16:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:16:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:16:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:16:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:47 | INFO     | VALIDATION | Generating enhanced outline for: 2.3 - Resume of Proposed Key Personnel
2025-08-03 15:17:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:17:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:48 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:17:48 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:17:48 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:53 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:53 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:17:53 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:53 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:17:53 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:17:53 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:17:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:17:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:17:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:17:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:17:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:18:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:18:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:18:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:18:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:18:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:18:58 | INFO     | VALIDATION | Generating enhanced outline for: 2.4 - Tentative/Contingent Offer Letter
2025-08-03 15:18:58 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:18:58 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:18:58 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:01 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:01 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:01 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:19:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:19:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:19:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:19:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:19:22 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:19:22 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:19:22 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:17 | INFO     | VALIDATION | Generating enhanced outline for: 3.0 - Tab C - Factor 2 - Management Approach
2025-08-03 15:20:17 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:17 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:17 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:19 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:19 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:19 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:27 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:20:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:20:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:20:32 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:20:32 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:20:32 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:15 | INFO     | VALIDATION | Generating enhanced outline for: 3.1 - Employee Turnover and Solutions
2025-08-03 15:21:15 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:15 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:15 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:18 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:18 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:18 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:31 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:31 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:31 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:31 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:21:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:21:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:21:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:21:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:21:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:33:20 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:33:20 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 15:33:20 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:33:20 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:33:20 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:33:20 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:33:35 | WARNING  | VALIDATION | Timeout getting context for Tab A - Proposal Cover/Transmittal Letter - using fallback
2025-08-03 15:33:48 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:35:45 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:35:45 | INFO     | VALIDATION | Starting enhanced government outline generation for iRiYNgd8RC
2025-08-03 15:35:45 | INFO     | VALIDATION | Generating enhanced outline for: 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:35:45 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:35:45 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:35:45 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:35:58 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:35:58 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:35:58 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:36:05 | ERROR    | VALIDATION | Timeout getting context for Tab A - Proposal Cover/Transmittal Letter
2025-08-03 15:36:22 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 15:36:22 | INFO     | VALIDATION | Section title: Test Section, Section Description: Test
2025-08-03 15:36:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 15:36:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 15:36:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 15:36:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 15:36:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 15:36:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 16:06:56 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 16:06:56 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 16:06:56 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 16:06:56 | INFO     | VALIDATION | Generating outline...
2025-08-03 16:06:56 | INFO     | VALIDATION | Processing 5 sections in parallel for outline generation
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:06:56 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:06:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:06:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:07:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:07:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:07:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:07:28 | WARNING  | VALIDATION | ChromaDB timeout for section Tab B - Factor 1 - Staffing & Key Personnel Qualifications - proceeding without context
2025-08-03 16:09:13 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 16:09:13 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:09:13 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:09:13 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:09:13 | WARNING  | VALIDATION | ChromaDB timeout for section Tab A - Proposal Cover/Transmittal Letter - proceeding without context
2025-08-03 16:10:29 | WARNING  | VALIDATION | ChromaDB timeout for section Tab C - Factor 2 - Management Approach - proceeding without context
2025-08-03 16:11:59 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 16:11:59 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:11:59 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:11:59 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:11:59 | WARNING  | VALIDATION | ChromaDB timeout for section Tab D - Factor 3 - Technical Approach - proceeding without context
2025-08-03 16:13:20 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 16:13:20 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:13:20 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:13:20 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:13:20 | WARNING  | VALIDATION | ChromaDB timeout for section Tab E - Factor 4 - Demonstrated Corporate Experience - proceeding without context
2025-08-03 16:14:40 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:14:40 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:14:40 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:14:40 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:14:40 | WARNING  | VALIDATION | ChromaDB timeout for section Recruitment, Hiring, and Retention Approach - proceeding without context
2025-08-03 16:15:45 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 16:15:45 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:15:45 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:15:45 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:15:45 | WARNING  | VALIDATION | ChromaDB timeout for section Employee Turnover and Solutions - proceeding without context
2025-08-03 16:17:18 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 16:17:18 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:17:18 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:17:18 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:17:18 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 1 – Program Management and Administration - proceeding without context
2025-08-03 16:18:44 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 16:18:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:18:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:18:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:18:44 | WARNING  | VALIDATION | ChromaDB timeout for section Experience Example 1 - proceeding without context
2025-08-03 16:20:10 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:20:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:20:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:20:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:20:10 | WARNING  | VALIDATION | ChromaDB timeout for section Certifications and Training Processes - proceeding without context
2025-08-03 16:21:36 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 16:21:36 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:21:36 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:21:36 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:21:36 | WARNING  | VALIDATION | ChromaDB timeout for section Surge Support Availability - proceeding without context
2025-08-03 16:23:05 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 16:23:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:23:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:23:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:23:05 | WARNING  | VALIDATION | ChromaDB timeout for section Experience Example 2 - proceeding without context
2025-08-03 16:24:28 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:24:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:24:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:24:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:24:28 | WARNING  | VALIDATION | ChromaDB timeout for section Resume of Proposed Key Personnel - proceeding without context
2025-08-03 16:25:41 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 16:25:41 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:25:41 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:25:41 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:25:41 | WARNING  | VALIDATION | ChromaDB timeout for section Quality Control and Performance Monitoring - proceeding without context
2025-08-03 16:26:59 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 2 – Information Management - proceeding without context
2025-08-03 16:28:21 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 16:28:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:28:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:28:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:28:21 | WARNING  | VALIDATION | ChromaDB timeout for section Experience Example 3 - proceeding without context
2025-08-03 16:29:06 | WARNING  | VALIDATION | ChromaDB timeout for section Tentative/Contingent Offer Letter - proceeding without context
2025-08-03 16:29:49 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 3 – Program Compliance - proceeding without context
2025-08-03 16:30:33 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 16:30:33 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:30:33 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:30:33 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:30:49 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:30:49 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:30:49 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:31:10 | WARNING  | VALIDATION | ChromaDB timeout for section TASK 4 – Training and Outreach - proceeding without context
2025-08-03 16:32:09 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 16:32:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:32:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:32:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:32:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:32:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:32:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:32:29 | INFO     | VALIDATION | Retrieved 3 context chunks for section: TASK 5 – Regulatory Support
2025-08-03 16:33:12 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 16:33:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:33:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:33:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:33:14 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:33:14 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:33:14 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:33:22 | INFO     | VALIDATION | Retrieved 3 context chunks for section: TASK 6 – Optional – Surge
2025-08-03 16:34:37 | INFO     | VALIDATION | Successfully generated 5 outlines
2025-08-03 16:34:37 | INFO     | VALIDATION | Outline generated successfully and saved to generated_outline_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2.json
2025-08-03 16:34:37 | INFO     | VALIDATION | Generated outline contains 5 main sections
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 1: Tab A - Proposal Cover/Transmittal Letter (0 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 2: Tab B - Factor 1 - Staffing & Key Personnel Qualifications (4 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 3: Tab C - Factor 2 - Management Approach (3 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 4: Tab D - Factor 3 - Technical Approach (6 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION |   Section 5: Tab E - Factor 4 - Demonstrated Corporate Experience (3 subsections)
2025-08-03 16:34:37 | INFO     | VALIDATION | Outline generation completed successfully!
2025-08-03 16:39:09 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 16:39:29 | INFO     | VALIDATION | get_opportunity called with opportunity_id=iRiYNgd8RC, tenant_id=8d9e9729-f7bd-44a0-9cf1-777f532a2db2, source=custom
2025-08-03 16:39:29 | INFO     | VALIDATION | Searching CUSTOM for opportunity_id=iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | CUSTOM search result: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-03 16:39:39 | INFO     | VALIDATION | Returning opportunity record: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-03 16:39:39 | INFO     | VALIDATION | Processing 5 sections in parallel for draft generation
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:39 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:39:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:39:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:52 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:39:52 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:39:52 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:39:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:39:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:39:56 | INFO     | VALIDATION | Retrieved RFP context for section: Tab A - Proposal Cover/Transmittal Letter
2025-08-03 16:39:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:39:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:39:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:39:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:39:56 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:39:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:39:57 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:39:57 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:39:57 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:40:02 | INFO     | VALIDATION | Retrieved client context for section: Tab A - Proposal Cover/Transmittal Letter
2025-08-03 16:40:47 | INFO     | VALIDATION | Retrieved RFP context for section: Tab C - Factor 2 - Management Approach
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Tab D - Factor 3 - Technical Approach
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:40:47 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:40:47 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:40:47 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:40:47 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:40:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:40:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:40:47 | ERROR    | VALIDATION | Error getting ChromaDB instance mapping for unique_id adeptengineeringsolutions and tenant_id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2: Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)
2025-08-03 16:42:10 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:42:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:42:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:42:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:42:10 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Tab D - Factor 3 - Technical Approach
2025-08-03 16:43:09 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 16:43:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:43:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:43:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:43:09 | WARNING  | VALIDATION | ChromaDB operation error for collection 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions:  - continuing without context
2025-08-03 16:44:51 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 16:44:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:44:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:44:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:44:51 | WARNING  | VALIDATION | ChromaDB operation error for collection 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions:  - continuing without context
2025-08-03 16:45:38 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:45:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 1 – Program Management and Administration
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Employee Turnover and Solutions
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Experience Example 1
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:45:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:45:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:45:40 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:45:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:45:49 | INFO     | VALIDATION | Retrieved client context for section: Experience Example 1
2025-08-03 16:46:26 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:46:26 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:46:26 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:46:26 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:46:26 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Employee Turnover and Solutions
2025-08-03 16:47:02 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 16:47:02 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:02 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:47:02 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:47:02 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: TASK 1 – Program Management and Administration
2025-08-03 16:47:29 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:47:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:47:29 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Recruitment, Hiring, and Retention Approach
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:47:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:29 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Experience Example 2
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:47:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:47:31 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:47:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:32 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Surge Support Availability
2025-08-03 16:47:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:47:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:47:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:47:35 | INFO     | VALIDATION | Retrieved client context for section: Experience Example 2
2025-08-03 16:48:35 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 16:48:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:48:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:48:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:48:35 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Surge Support Availability
2025-08-03 16:49:41 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:49:41 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:49:41 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 2 – Information Management
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:49:41 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:49:41 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:49:41 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Recruitment, Hiring, and Retention Approach
2025-08-03 16:50:46 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:50:46 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:50:46 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Experience Example 3
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:50:46 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:50:46 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Quality Control and Performance Monitoring
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:50:46 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:50:46 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:50:46 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: TASK 2 – Information Management
2025-08-03 16:51:51 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:51:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:51:51 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Certifications and Training Processes
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:51:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:51:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:51:51 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Quality Control and Performance Monitoring
2025-08-03 16:52:38 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 3 – Program Compliance
2025-08-03 16:52:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:52:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:52:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:52:38 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Certifications and Training Processes
2025-08-03 16:53:30 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 16:53:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:53:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:53:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:53:30 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: Experience Example 3
2025-08-03 16:54:21 | WARNING  | VALIDATION | ChromaDB timeout for client context in section: TASK 3 – Program Compliance
2025-08-03 16:55:11 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:55:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:55:11 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: Resume of Proposed Key Personnel
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:55:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:55:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:55:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:55:21 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:55:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:55:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:55:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:55:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:55:24 | INFO     | VALIDATION | Retrieved client context for section: Resume of Proposed Key Personnel
2025-08-03 16:56:04 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:56:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:56:04 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 4 – Training and Outreach
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:56:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:56:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:56:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:56:04 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:56:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:56:24 | INFO     | VALIDATION | Retrieved client context for section: TASK 4 – Training and Outreach
2025-08-03 16:57:00 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 16:57:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:57:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:57:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:57:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:57:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:57:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:57:07 | INFO     | VALIDATION | Retrieved RFP context for section: Tentative/Contingent Offer Letter
2025-08-03 16:57:07 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:07 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:57:07 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:07 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:57:07 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:57:07 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:14 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:57:14 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:57:14 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:57:18 | INFO     | VALIDATION | Retrieved client context for section: Tentative/Contingent Offer Letter
2025-08-03 16:57:44 | WARNING  | VALIDATION | ChromaDB timeout for RFP context in section: TASK 5 – Regulatory Support
2025-08-03 16:57:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:57:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:57:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:44 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:57:44 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:57:44 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:57:46 | INFO     | VALIDATION | Retrieved client context for section: TASK 5 – Regulatory Support
2025-08-03 16:58:37 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 16:58:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 16:58:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 16:58:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:58:39 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 16:58:39 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 16:58:39 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 16:58:44 | INFO     | VALIDATION | Retrieved RFP context for section: TASK 6 – Optional – Surge
2025-08-03 16:58:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-03 16:58:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-03 16:58:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:58:45 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-03 16:58:45 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-03 16:58:45 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-03 16:58:48 | INFO     | VALIDATION | Retrieved client context for section: TASK 6 – Optional – Surge
2025-08-03 16:59:27 | INFO     | VALIDATION | Successfully generated 5 draft sections
2025-08-03 16:59:58 | INFO     | VALIDATION | Updated CustomOppsTable record with opportunity_id=iRiYNgd8RC
