#!/usr/bin/env python3
"""
Simple Execution Script for Proposal Outline Generation Demo

This script provides an easy way to run the outline generation system
with different configurations and test scenarios.

Usage:
    python run_outline_demo.py [--mode test|demo] [--env development|production|government]
"""

import argparse
import asyncio
import sys
import os
from pathlib import Path

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner():
    """Print a nice banner for the demo."""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🏛️  ENHANCED GOVERNMENT PROPOSAL OUTLINE GENERATOR 🏛️     ║
║                                                              ║
║    ✨ Features:                                              ║
║    • Government Compliance Validation                       ║
║    • Parallel Processing for Performance                    ║
║    • Enhanced Prompt Engineering                            ║
║    • Intelligent Caching System                             ║
║    • Real-time Quality Scoring                              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """)


async def run_test_mode():
    """Run the test mode (no external dependencies)."""
    print("🧪 Running Test Mode (No External Dependencies)")
    print("=" * 60)
    
    try:
        from test_outline_system import main as test_main
        await test_main()
    except ImportError as e:
        print(f"❌ Could not import test module: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True


async def run_demo_mode(environment: str = "production"):
    """Run the full demo mode (requires external services)."""
    print(f"🚀 Running Full Demo Mode (Environment: {environment})")
    print("=" * 60)
    
    try:
        from demo_outline_generation import OutlineGenerationDemo
        demo = OutlineGenerationDemo(environment=environment)
        await demo.run_complete_demo()
    except ImportError as e:
        print(f"❌ Could not import demo module: {e}")
        print("💡 Try running in test mode: python run_outline_demo.py --mode test")
        return False
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print("💡 Check that all services are running and accessible")
        return False
    
    return True


def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking Dependencies...")
    
    missing_deps = []
    
    # Check core dependencies
    try:
        import asyncio
        import json
        from pathlib import Path
        print("✅ Core Python modules: OK")
    except ImportError as e:
        missing_deps.append(f"Core modules: {e}")
    
    # Check if validation components are available
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from services.proposal.validation.government_compliance_validator import GovernmentComplianceValidator
        from services.proposal.config.outline_config import get_config_by_environment
        print("✅ Validation components: OK")
    except ImportError as e:
        print(f"⚠️  Validation components: {e}")
        print("   (Test mode will still work)")
    
    # Check if full service dependencies are available
    try:
        from services.proposal.outline import ProposalOutlineService
        print("✅ Outline service: OK")
    except ImportError as e:
        print(f"⚠️  Outline service: {e}")
        print("   (Only test mode available)")
    
    if missing_deps:
        print("\n❌ Missing Dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        return False
    
    print("✅ All dependencies available")
    return True


def create_results_directory():
    """Create results directory if it doesn't exist."""
    results_dir = Path("demo_results")
    results_dir.mkdir(exist_ok=True)
    
    test_results_dir = Path("test_results")
    test_results_dir.mkdir(exist_ok=True)
    
    print(f"📁 Results will be saved to: {results_dir.absolute()}")
    return results_dir


def print_usage_examples():
    """Print usage examples."""
    print("""
📖 Usage Examples:

1. Run basic test (no external dependencies):
   python run_outline_demo.py --mode test

2. Run full demo with production settings:
   python run_outline_demo.py --mode demo --env production

3. Run demo with government-strict settings:
   python run_outline_demo.py --mode demo --env government

4. Run demo with development settings (more creative):
   python run_outline_demo.py --mode demo --env development

🔧 Environments:
   • development: More creative, lower compliance thresholds
   • production:  Balanced settings for real-world use
   • government:  Strictest compliance, maximum consistency

📁 Output:
   • Test results: ./test_results/
   • Demo results: ./demo_results/
    """)


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Enhanced Government Proposal Outline Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--mode", 
        choices=["test", "demo"], 
        default="test",
        help="Run mode: 'test' for basic testing, 'demo' for full demonstration"
    )
    
    parser.add_argument(
        "--env", 
        choices=["development", "production", "government"], 
        default="production",
        help="Environment configuration for demo mode"
    )
    
    parser.add_argument(
        "--check-deps", 
        action="store_true",
        help="Check dependencies and exit"
    )
    
    parser.add_argument(
        "--examples", 
        action="store_true",
        help="Show usage examples and exit"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Handle special flags
    if args.examples:
        print_usage_examples()
        return
    
    if args.check_deps:
        success = check_dependencies()
        sys.exit(0 if success else 1)
    
    # Create results directory
    create_results_directory()
    
    # Check dependencies
    if not check_dependencies():
        print("\n⚠️  Some dependencies are missing. Continuing with available features...")
    
    print()
    
    # Run the selected mode
    try:
        if args.mode == "test":
            success = await run_test_mode()
        else:  # demo mode
            success = await run_demo_mode(args.env)
        
        if success:
            print("\n🎉 Execution completed successfully!")
            print("📁 Check the results directory for generated files.")
        else:
            print("\n❌ Execution failed. Check the logs for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Execution interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
